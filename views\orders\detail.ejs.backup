<%- include('../partials/header') %>

  <!-- 成功提示 -->
  <% if (created) { %>
    <div class="alert alert-success alert-dismissible fade show" role="alert">
      <i class="fas fa-check-circle me-2"></i>接单成功！订单已创建�? <button type="button" class="btn-close"
        data-bs-dismiss="alert"></button>
    </div>
    <% } %>

      <% if (typeof updated !=='undefined' && updated) { %>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
          <i class="fas fa-check-circle me-2"></i>订单更新成功�? <button type="button" class="btn-close"
            data-bs-dismiss="alert"></button>
        </div>
        <% } %>

          <!-- 错误提示 -->
          <% if (typeof error !=='undefined' && error) { %>
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
              <i class="fas fa-exclamation-circle me-2"></i>
              <%= error %>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
            <% } %>

              <!-- 页面标题和操作按�?-->
              <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-file-invoice me-2"></i>
                  <%= title %>
                </h2>
                <div>
                  <a href="/orders" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-1"></i>返回列表
                  </a>
                  <% if (order.qr_code_path) { %>
                    <button class="btn btn-outline-info"
                      onclick="showQRCode('<%= order.qr_code_path %>', '<%= order.order_no %>')">
                      <i class="fas fa-qrcode me-1"></i>查看二维�? </button>
                    <% } %>
                      <% if (user.role==='admin' ) { %>
                        <button class="btn btn-outline-danger" onclick="deleteOrder(<%= order.id %>)">
                          <i class="fas fa-trash me-1"></i>删除订单
                        </button>
                        <% } %>
                </div>
              </div>

              <!-- 订单基本信息 -->
              <div class="row">
                <div class="col-lg-8">
                  <div class="card mb-4">
                    <div class="card-header">
                      <h5 class="mb-0"><i class="fas fa-info-circle me-2"></i>订单信息</h5>
                    </div>
                    <div class="card-body">
                      <div class="row">
                        <div class="col-md-6">
                          <table class="table table-borderless">
                            <tr>
                              <td class="text-muted">订单�?</td>
                              <td class="fw-bold">
                                <span id="orderNo">
                                  <%= order.order_no %>
                                </span>
                                <button class="btn btn-sm btn-outline-secondary ms-2" onclick="copyOrderNo()" title="复制订单�?>
                                  <i class=" fas fa-copy"></i>
                                </button>
                              </td>
                            </tr>
                            <tr>
                              <td class="text-muted">
                                <% if (order.report_type==='self_reported' ) { %>
                                  项目标题:
                                  <% } else { %>
                                    任务标题:
                                    <% } %>
                              </td>
                              <td>
                                <% if (order.report_type==='self_reported' ) { %>
                                  <%= order.report_title || '未知项目' %>
                                    <% } else { %>
                                      <%= order.task_title || '未知任务' %>
                                        <% } %>
                              </td>
                            </tr>
                            <tr>
                              <td class="text-muted">订单状�?</td>
                              <td>
                                <span class="badge bg-<%= order.status === 'pending_deposit' ? 'warning' :
                                          order.status === 'in_progress' ? 'info' :
                                          order.status === 'pending_final' ? 'warning' :
                                          order.status === 'completed' ? 'success' :
                                          order.status === 'pending_review' ? 'primary' : 'secondary' %>">
                                  <%= order.status_name %>
                                </span>
                              </td>
                            </tr>

                          </table>
                        </div>
                        <div class="col-md-6">
                          <table class="table table-borderless">
                            <!-- 写手信息已隐�?-->
                            <tr>
                              <td class="text-muted">创建时间:</td>
                              <td>
                                <%= order.formatted_created_at %>
                              </td>
                            </tr>
                            <tr>
                              <td class="text-muted">更新时间:</td>
                              <td>
                                <%= order.formatted_updated_at %>
                              </td>
                            </tr>
                          </table>
                        </div>
                      </div>

                      <!-- 自主申报订单详细信息 -->
                      <% if (order.report_type==='self_reported' ) { %>
                        <div class="mt-4">
                          <h6 class="text-primary mb-3">
                            <i class="fas fa-info-circle me-2"></i>申报项目详情
                          </h6>
                          <div class="row">
                            <div class="col-md-6">
                              <table class="table table-borderless">
                                <tr>
                                  <td class="text-muted">项目分类:</td>
                                  <td>
                                    <%= order.report_category || '未知' %>
                                  </td>
                                </tr>
                                <tr>
                                  <td class="text-muted">项目难度:</td>
                                  <td>
                                    <% const difficultyNames={ 'easy' : '简单' , 'medium' : '中等' , 'hard' : '困难' }; %>
                                      <span class="badge bg-<%= order.report_difficulty === 'easy' ? 'success' :
                                      order.report_difficulty === 'medium' ? 'warning' : 'danger' %>">
                                        <%= difficultyNames[order.report_difficulty] || order.report_difficulty || '未知'
                                          %>
                                      </span>
                                  </td>
                                </tr>
                                <% if (order.deadline) { %>
                                  <tr>
                                    <td class="text-muted">截止时间:</td>
                                    <td>
                                      <%= new Date(order.deadline).toLocaleString('zh-CN') %>
                                    </td>
                                  </tr>
                                  <% } %>
                              </table>
                            </div>
                            <div class="col-md-6">
                              <% if (order.report_description) { %>
                                <div class="mb-3">
                                  <label class="text-muted">项目描述:</label>
                                  <div class="border rounded p-2 bg-light">
                                    <%= order.report_description %>
                                  </div>
                                </div>
                                <% } %>
                                  <% if (order.work_details) { %>
                                    <div class="mb-3">
                                      <label class="text-muted">工作量说�?</label>
                                      <div class="border rounded p-2 bg-light">
                                        <%= order.work_details %>
                                      </div>
                                    </div>
                                    <% } %>
                            </div>
                          </div>
                        </div>
                        <% } %>

                          <% if (order.notes) { %>
                            <div class="mt-3">
                              <h6 class="text-muted">备注信息:</h6>
                              <p class="border p-3 rounded bg-light">
                                <%= order.notes %>
                              </p>
                            </div>
                            <% } %>
                    </div>
                  </div>

                  <!-- 任务内容 -->
                  <% if (order.task) { %>
                    <div class="card mb-4">
                      <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-tasks me-2"></i>任务内容</h5>
                      </div>
                      <div class="card-body">
                        <!-- 任务描述 -->
                        <% if (order.task.description && order.task.description.trim() !=='' ) { %>
                          <div class="mb-4">
                            <div class="d-flex justify-content-between align-items-center mb-2">
                              <h6 class="text-primary mb-0">
                                <i class="fas fa-info-circle me-1"></i>任务描述
                              </h6>
                              <% if (user.role==='admin' || user.role==='writer' ) { %>
                                <button type="button" class="btn btn-outline-primary btn-sm"
                                  onclick="editTaskDescription()">
                                  <i class="fas fa-edit me-1"></i>编辑
                                </button>
                                <% } %>
                            </div>
                            <div class="p-3 bg-light rounded">
                              <p class="mb-0" style="white-space: pre-wrap;" id="taskDescription">
                                <%= order.task.description %>
                              </p>
                            </div>
                          </div>
                          <% } else { %>
                            <div class="mb-4">
                              <div class="d-flex justify-content-between align-items-center mb-2">
                                <h6 class="text-primary mb-0">
                                  <i class="fas fa-info-circle me-1"></i>任务描述
                                </h6>
                                <% if (user.role==='admin' || user.role==='writer' ) { %>
                                  <button type="button" class="btn btn-outline-primary btn-sm"
                                    onclick="editTaskDescription()">
                                    <i class="fas fa-plus me-1"></i>添加描述
                                  </button>
                                  <% } %>
                              </div>
                              <div class="p-3 bg-light rounded text-muted">
                                <p class="mb-0" id="taskDescription">暂无任务描述</p>
                              </div>
                            </div>
                            <% } %>

                              <!-- 任务要求 -->
                              <% if (order.task.requirements && order.task.requirements.trim() !=='' ) { %>
                                <div class="mb-3">
                                  <div class="d-flex justify-content-between align-items-center mb-2">
                                    <h6 class="text-warning mb-0">
                                      <i class="fas fa-list-check me-1"></i>任务要求
                                    </h6>
                                    <% if (user.role==='admin' || user.role==='writer' ) { %>
                                      <button type="button" class="btn btn-outline-warning btn-sm"
                                        onclick="editTaskRequirements()">
                                        <i class="fas fa-edit me-1"></i>编辑
                                      </button>
                                      <% } %>
                                  </div>
                                  <div class="p-3 bg-light rounded">
                                    <p class="mb-0" style="white-space: pre-wrap;" id="taskRequirements">
                                      <%= order.task.requirements %>
                                    </p>
                                  </div>
                                </div>
                                <% } else { %>
                                  <div class="mb-3">
                                    <div class="d-flex justify-content-between align-items-center mb-2">
                                      <h6 class="text-warning mb-0">
                                        <i class="fas fa-list-check me-1"></i>任务要求
                                      </h6>
                                      <% if (user.role==='admin' || user.role==='writer' ) { %>
                                        <button type="button" class="btn btn-outline-warning btn-sm"
                                          onclick="editTaskRequirements()">
                                          <i class="fas fa-plus me-1"></i>添加要求
                                        </button>
                                        <% } %>
                                    </div>
                                    <div class="p-3 bg-light rounded text-muted">
                                      <p class="mb-0" id="taskRequirements">暂无任务要求</p>
                                    </div>
                                  </div>
                                  <% } %>

                                    <!-- 任务基本信息 -->
                                    <div class="row">
                                      <div class="col-md-6">
                                        <small class="text-muted">截止时间:</small>
                                        <div class="fw-bold">
                                          <%= order.task.formatted_deadline %>
                                        </div>
                                      </div>
                                      <div class="col-md-6">
                                        <small class="text-muted">任务状�?</small>
                                        <div>
                                          <span class="badge bg-<%= order.task.status === 'published' ? 'success' :
                                order.task.status === 'in_progress' ? 'warning' :
                                order.task.status === 'completed' ? 'primary' : 'secondary' %>">
                                            <%= order.task.status_name %>
                                          </span>
                                        </div>
                                      </div>
                                    </div>
                      </div>
                    </div>
                    <% } %>



                      <!-- 订单状态历�?-->
                      <div class="card mb-4">
                        <div class="card-header bg-light">
                          <h5 class="mb-0"><i class="fas fa-history me-2"></i>状态历�?/h5>
                        </div>
                        <div class="card-body">
                          <% if (typeof statusHistory !=='undefined' && statusHistory.length> 0) { %>
                            <div class="timeline">
                              <% statusHistory.forEach((history, index)=> { %>
                                <div class="timeline-item <%= index === 0 ? 'timeline-item-current' : '' %>">
                                  <div class="timeline-marker">
                                    <% if (index===0) { %>
                                      <i class="fas fa-circle text-primary"></i>
                                      <% } else { %>
                                        <i class="fas fa-check-circle text-success"></i>
                                        <% } %>
                                  </div>
                                  <div class="timeline-content">
                                    <div class="d-flex justify-content-between align-items-start">
                                      <div>
                                        <h6 class="mb-1">
                                          <% if (history.from_status) { %>
                                            <%= history.from_status_name %> �?<%= history.to_status_name %>
                                                <% } else { %>
                                                  <%= history.to_status_name %>
                                                    <% } %>
                                        </h6>
                                        <p class="text-muted mb-1">
                                          操作�? <%= history.operator_name %>
                                        </p>
                                        <% if (history.notes) { %>
                                          <p class="text-muted small mb-0">
                                            备注: <%= history.notes %>
                                          </p>
                                          <% } %>
                                      </div>
                                      <small class="text-muted">
                                        <%= history.formatted_created_at %>
                                      </small>
                                    </div>
                                  </div>
                                </div>
                                <% }) %>
                            </div>
                            <% } else { %>
                              <p class="text-muted text-center">暂无状态历史记�?/p>
                                <% } %>
                        </div>
                      </div>

                      <!-- 状态更�?-->
                      <% const canUpdateStatus=order.can_edit && order.status !=='completed' && order.status
                        !=='cancelled' && !(order.status==='pending_review' && user.role==='writer' ); %>

                        <!-- pending_review状态提�?-->
                        <% if (order.status==='pending_review' && user.role==='writer' ) { %>
                          <div class="alert alert-info mb-4">
                            <i class="fas fa-info-circle me-2"></i>
                            <strong>申报审核�?/strong>
                              <p class="mb-0 mt-2">您的自主申报订单正在等待管理员审核，审核通过后您将可以进行后续操作�?/p>
                          </div>
                          <% } %>

                            <% if (canUpdateStatus) { %>
                              <div class="card mb-4">
                                <div class="card-header">
                                  <h5 class="mb-0"><i class="fas fa-edit me-2"></i>状态管�?/h5>
                                </div>
                                <div class="card-body">
                                  <form id="statusForm" method="POST" action="/orders/<%= order.id %>/status">
                                    <div class="row">
                                      <div class="col-md-6">
                                        <label class="form-label">更新状�?/label>
                                          <select class="form-select" name="status" id="statusSelect">
                                            <option value="">选择新状�?/option>
                                              <% if (order.valid_transitions && order.valid_transitions.length> 0) { %>
                                                <% order.valid_transitions.forEach(status=> { %>
                                                  <% const statusLabels={ 'pending_review' : '待审�? , ' pending_deposit'
                                                    : '等待定金' , 'in_progress' : '进行�? , ' pending_final' : '等待尾款'
                                                    , 'completed' : '已完�? , ' cancelled' : '已取�? }; %>
                                                <option value="<%= status %>">
                                                  <%= statusLabels[status] || status %>
                                                </option>
                                                <% }); %>
                                                  <% } else { %>
                                                    <option disabled>当前状态无法转�?/option>
                                                    <% } %>
                                        </select>
                                      </div>
                                      <div class="col-md-6 d-flex align-items-end">
                                        <button type="submit" class="btn btn-primary">
                                          <i class="fas fa-save me-1"></i>更新状�?                                        </button>
                                      </div>
                                    </div>
                                  </form>
                                </div>
                              </div>
                              <% } %>

                                <!-- 转单功能 -->
                                <% const canTransfer=user.role===' writer' && order.writer_id===user.id &&
                                                    ['pending_deposit', 'in_progress' , 'pending_final'
                                                    ].includes(order.status) && !order.is_transferred &&
                                                    !order.has_pending_transfer && transferConfig &&
                                                    transferConfig.enabled; %>

                                                    <% if (canTransfer) { %>
                                                      <div class="card mb-4">
                                                        <div class="card-header">
                                                          <h5 class="mb-0"><i class="fas fa-exchange-alt me-2"></i>转单功能
                                                          </h5>
                                                        </div>
                                                        <div class="card-body">
                                                          <% if (order.transfer_count && order.transfer_count> 0) { %>
                                                            <div class="alert alert-info small mb-3">
                                                              <i class="fas fa-info-circle me-2"></i>
                                                              此订单已转单 <%= order.transfer_count %> �?
                                                            </div>
                                                            <% } %>

                                                              <p class="text-muted small mb-3">
                                                                将订单转到任务大厅，让其他写手接单。转单后订单将在大厅显示3小时，超时后自动退回�? </p>

                                                              <form id="transferForm" method="POST"
                                                                action="/orders/<%= order.id %>/transfer">
                                                                <div class="mb-3">
                                                                  <label for="transferReason"
                                                                    class="form-label">转单原因（可选）</label>
                                                                  <textarea class="form-control" id="transferReason"
                                                                    name="reason" rows="2" maxlength="500"
                                                                    placeholder="请简要说明转单原�?.."></textarea>
                                                                  <div class="form-text">最�?00个字�?/div>
                                                                  </div>

                                                                  <div class="d-flex gap-2">
                                                                    <button type="button" class="btn btn-warning"
                                                                      onclick="confirmTransfer()">
                                                                      <i class="fas fa-exchange-alt me-2"></i>申请转单
                                                                    </button>
                                                                  </div>
                                                              </form>
                                                        </div>
                                                      </div>
                                                      <% } %>

                                                        <!-- 待处理转单提�?-->
                                                        <% if (order.has_pending_transfer && user.role==='writer' &&
                                                          order.writer_id===user.id && transferConfig &&
                                                          transferConfig.enabled) { %>
                                                          <div class="card mb-4">
                                                            <div class="card-header bg-info text-white">
                                                              <h5 class="mb-0"><i
                                                                  class="fas fa-exchange-alt me-2"></i>转单进行�?/h5>
                                                            </div>
                                                            <div class="card-body">
                                                              <div class="alert alert-info mb-0">
                                                                <i class="fas fa-info-circle me-2"></i>
                                                                <strong>转单申请已提�?/strong>
                                                                  <p class="mb-0 mt-2">
                                                                    您的转单申请正在任务大厅等待其他写手接单，在此期间无法再次发起转单�?/p>
                                                              </div>
                                                            </div>
                                                          </div>
                                                          <% } %>

                                                            <!-- 转单状态显�?-->
                                                            <% if (order.is_transferred && transferConfig &&
                                                              transferConfig.enabled) { %>
                                                              <div class="card mb-4">
                                                                <div class="card-header bg-warning text-dark">
                                                                  <h5 class="mb-0"><i
                                                                      class="fas fa-exchange-alt me-2"></i>转单进度</h5>
                                                                </div>
                                                                <div class="card-body">
                                                                  <!-- 转单进度�?-->
                                                                  <div class="mb-3">
                                                                    <div
                                                                      class="d-flex justify-content-between align-items-center mb-2">
                                                                      <span class="text-muted small">转单状�?/span>
                                                                        <span
                                                                          class="badge bg-warning text-dark">等待接单</span>
                                                                    </div>
                                                                    <div class="progress" style="height: 8px;">
                                                                      <div class="progress-bar bg-warning"
                                                                        role="progressbar" style="width: 50%"></div>
                                                                    </div>
                                                                    <div class="d-flex justify-content-between mt-1">
                                                                      <small class="text-muted">已发�?/small>
                                                                        <small class="text-muted">等待接单</small>
                                                                        <small class="text-muted">已接�?/small>
                                                                    </div>
                                                                  </div>

                                                                  <div class="alert alert-warning mb-3">
                                                                    <i class="fas fa-info-circle me-2"></i>
                                                                    <strong>订单正在任务大厅等待其他写手接单</strong>
                                                                  </div>

                                                                  <div class="row mb-3">
                                                                    <div class="col-md-6">
                                                                      <small class="text-muted">发布时间�?/small><br>
                                                                        <span id="transferTime">加载�?..</span>
                                                                    </div>
                                                                    <div class="col-md-6">
                                                                      <small class="text-muted">剩余时间�?/small><br>
                                                                        <span id="transferCountdown"
                                                                          class="text-warning fw-bold">计算�?..</span>
                                                                    </div>
                                                                  </div>

                                                                  <p class="text-muted small mb-3">
                                                                    如果超过3小时无人接单，订单将自动退回给您。您也可以主动取消转单�? </p>

                                                                  <button type="button"
                                                                    class="btn btn-outline-secondary btn-sm"
                                                                    onclick="cancelTransfer()">
                                                                    <i class="fas fa-times me-2"></i>取消转单
                                                                  </button>
                                                                </div>
                                                              </div>
                                                              <% } %>

                                                                <!-- 代部署功�?-->
                                                                <% const canCreateDeployment=user.role==='writer' &&
                                                                  order.writer_id===user.id &&
                                                                  ['pending_deposit', 'in_progress' , 'pending_final'
                                                                  ].includes(order.status) && !order.has_deployment_task
                                                                  && (!order.is_transferred || !transferConfig ||
                                                                  !transferConfig.enabled); %>

                                                                  <% if (canCreateDeployment) { %>
                                                                    <div class="card mb-4">
                                                                      <div class="card-header">
                                                                        <h5 class="mb-0"><i
                                                                            class="fas fa-rocket me-2"></i>代部署功�?/h5>
                                                                      </div>
                                                                      <div class="card-body">
                                                                        <p class="text-muted small mb-3">
                                                                          如果您没有时间为客户部署项目，可以委托其他写手代为部署。代部署佣金固定�?0元，将从您的订单佣金中扣除�?
                                                                        </p>

                                                                        <% if (order.commission_amount < 10) { %>
                                                                          <div class="alert alert-warning small mb-3">
                                                                            <i
                                                                              class="fas fa-exclamation-triangle me-2"></i>
                                                                            订单佣金不足10元，无法创建代部署任�?
                                                                          </div>
                                                                          <% } else { %>
                                                                            <form id="deploymentForm" method="POST"
                                                                              action="/orders/<%= order.id %>/deployment"
                                                                              enctype="multipart/form-data">
                                                                              <div class="mb-3">
                                                                                <label for="deploymentDescription"
                                                                                  class="form-label">部署说明</label>
                                                                                <textarea class="form-control"
                                                                                  id="deploymentDescription"
                                                                                  name="description" rows="3"
                                                                                  maxlength="1000"
                                                                                  placeholder="请详细说明部署要求、环境配置、注意事项等..."></textarea>
                                                                                <div class="form-text">
                                                                                  最�?000个字符，详细的说明有助于其他写手更好地完成部�?/div>
                                                                                </div>

                                                                                <div class="mb-3">
                                                                                  <label for="sourceCodeFile"
                                                                                    class="form-label">源代码文�?<span
                                                                                      class="text-muted">(可�?</span></label>
                                                                                  <input type="file"
                                                                                    class="form-control"
                                                                                    id="sourceCodeFile"
                                                                                    name="sourceCode"
                                                                                    accept=".zip,.rar,.7z,.tar,.gz,.txt,.js,.html,.css,.json,.md">
                                                                                  <div class="form-text">
                                                                                    支持压缩包（ZIP、RAR�?Z等）或代码文件，最�?0MB。上传源代码可以让部署人员更方便地完成部署工作�?
                                                                                  </div>
                                                                                </div>

                                                                                <div
                                                                                  class="alert alert-info small mb-3">
                                                                                  <i
                                                                                    class="fas fa-info-circle me-2"></i>
                                                                                  <strong>代部署说明：</strong><br>
                                                                                  �?代部署佣金：<strong>10�?/strong>（从您的订单佣金中扣除）<br>
                                                                                    �?任务将发布到任务大厅，长期有效直到被接单<br>
                                                                                    �?部署完成后，10元将自动转给部署写手
                                                                                </div>

                                                                                <div class="d-flex gap-2">
                                                                                  <button type="button"
                                                                                    class="btn btn-primary"
                                                                                    onclick="confirmCreateDeployment()">
                                                                                    <i
                                                                                      class="fas fa-rocket me-2"></i>创建代部署任�?
                                                                                  </button>
                                                                                </div>
                                                                            </form>
                                                                            <% } %>
                                                                      </div>
                                                                    </div>
                                                                    <% } %>

                                                                      <!-- 代部署状态显�?-->
                                                                      <% if (order.has_deployment_task ||
                                                                        (user.role==='writer' && order.writer_id
                                                                        !==user.id)) { %>
                                                                        <div class="card mb-4"
                                                                          id="deploymentStatusCard">
                                                                          <div class="card-header bg-info text-white">
                                                                            <h5 class="mb-0"><i
                                                                                class="fas fa-rocket me-2"></i>代部署进�?/h5>
                                                                          </div>
                                                                          <div class="card-body">
                                                                            <!-- 代部署进度条 -->
                                                                            <div class="mb-3" id="deploymentProgress">
                                                                              <div
                                                                                class="d-flex justify-content-between align-items-center mb-2">
                                                                                <span
                                                                                  class="text-muted small">部署状�?/span>
                                                                                  <span class="badge bg-info"
                                                                                    id="deploymentStatusBadge">加载�?..</span>
                                                                              </div>
                                                                              <div class="progress"
                                                                                style="height: 8px;">
                                                                                <div class="progress-bar bg-info"
                                                                                  role="progressbar" style="width: 0%"
                                                                                  id="deploymentProgressBar"></div>
                                                                              </div>
                                                                              <div
                                                                                class="d-flex justify-content-between mt-1">
                                                                                <small class="text-muted">已发�?/small>
                                                                                  <small class="text-muted">已接�?/small>
                                                                                    <small
                                                                                      class="text-muted">已完�?/small>
                                                                              </div>
                                                                            </div>

                                                                            <div id="deploymentTaskInfo">
                                                                              <div class="text-center">
                                                                                <div class="spinner-border text-primary"
                                                                                  role="status">
                                                                                  <span
                                                                                    class="visually-hidden">加载�?..</span>
                                                                                </div>
                                                                                <p class="mt-2">正在加载代部署任务信�?..</p>
                                                                              </div>
                                                                            </div>
                                                                          </div>
                                                                        </div>
                                                                        <% } %>

                                                                          <!-- 微信群二维码 -->
                                                                          <div class="card mb-4">
                                                                            <div class="card-header">
                                                                              <h5 class="mb-0"><i
                                                                                  class="fas fa-qrcode me-2"></i>微信群二维码
                                                                              </h5>
                                                                            </div>
                                                                            <div class="card-body">
                                                                              <% if (order.task &&
                                                                                order.task.wechat_qr_code) { %>
                                                                                <div class="text-center mb-3">
                                                                                  <img
                                                                                    src="/<%= order.task.wechat_qr_code %>"
                                                                                    alt="微信群二维码" class="img-fluid"
                                                                                    style="max-width: 300px; max-height: 300px;">
                                                                                </div>
                                                                                <div
                                                                                  class="text-muted small text-center mb-3">
                                                                                  上传时间�?%=
                                                                                  order.task.wechat_qr_code_uploaded_at
                                                                                  ? new
                                                                                  Date(order.task.wechat_qr_code_uploaded_at).toLocaleString('zh-CN')
                                                                                  : '未知' %>
                                                                                </div>
                                                                                <% } else { %>
                                                                                  <div
                                                                                    class="text-center text-muted mb-3">
                                                                                    <i
                                                                                      class="fas fa-qrcode fa-3x mb-2"></i>
                                                                                    <p>暂无微信群二维码</p>
                                                                                  </div>
                                                                                  <% } %>

                                                                                    <!-- 上传表单 -->
                                                                                    <form id="qrCodeUploadForm"
                                                                                      enctype="multipart/form-data">
                                                                                      <div class="mb-3">
                                                                                        <label for="qrCodeFile"
                                                                                          class="form-label">
                                                                                          <%= (order.task &&
                                                                                            order.task.wechat_qr_code)
                                                                                            ? '更新' : '上传' %>微信群二维码
                                                                                        </label>
                                                                                        <input type="file"
                                                                                          class="form-control"
                                                                                          id="qrCodeFile" name="qrCode"
                                                                                          accept="image/*" required>
                                                                                        <div class="form-text">
                                                                                          支持JPG、PNG、GIF、WebP格式，最�?0MB
                                                                                        </div>
                                                                                      </div>

                                                                                      <div class="d-flex gap-2">
                                                                                        <button type="button"
                                                                                          class="btn btn-primary"
                                                                                          onclick="uploadQRCode()">
                                                                                          <i
                                                                                            class="fas fa-upload me-2"></i>
                                                                                          <%= (order.task &&
                                                                                            order.task.wechat_qr_code)
                                                                                            ? '更新' : '上传' %>二维�?
                                                                                        </button>
                                                                                        <% if (order.task &&
                                                                                          order.task.wechat_qr_code) {
                                                                                          %>
                                                                                          <a href="/<%= order.task.wechat_qr_code %>"
                                                                                            target="_blank"
                                                                                            class="btn btn-outline-info">
                                                                                            <i
                                                                                              class="fas fa-eye me-2"></i>查看大图
                                                                                          </a>
                                                                                          <% } %>
                                                                                      </div>
                                                                                    </form>

                                                                                    <div
                                                                                      class="alert alert-info small mt-3">
                                                                                      <i
                                                                                        class="fas fa-info-circle me-2"></i>
                                                                                      <strong>说明�?/strong>所有用户都可以上传或更新微信群二维码，方便大家加入项目交流群�?
                                                                                    </div>
                                                                            </div>
                                                                          </div>
                                      </div>

                                      <!-- 侧边�?-->
                                      <div class="col-lg-4">
                                        <!-- 支付状态和佣金信息 -->
                                        <div class="card mb-4">
                                          <div class="card-header">
                                            <h5 class="mb-0"><i class="fas fa-money-check-alt me-2"></i>支付状�?/h5>
                                          </div>
                                          <div class="card-body">
                                            <!-- 定金状�?-->
                                            <div
                                              class="d-flex justify-content-between align-items-center mb-3 p-3 bg-light rounded">
                                              <div>
                                                <h6 class="mb-1">定金支付</h6>
                                                <small class="text-muted">Deposit Payment</small>
                                              </div>
                                              <div class="text-center">
                                                <% if (user.role==='admin' ) { %>
                                                  <!-- 管理员显示基于滑块状态，与写手保持一�?-->
                                                  <% const adminDepositHasAmount=parseFloat(order.deposit_amount || 0)>
                                                    0;
                                                    const adminDepositStatus = adminDepositHasAmount ||
                                                    order.deposit_paid_status;
                                                    %>
                                                    <div
                                                      class="<%= adminDepositStatus ? 'text-success' : 'text-muted' %> fs-4">
                                                      <i
                                                        class="fas fa-<%= adminDepositStatus ? 'check-circle' : 'times-circle' %>"></i>
                                                    </div>
                                                    <small
                                                      class="<%= adminDepositStatus ? 'text-success' : 'text-muted' %>">
                                                      <%= adminDepositStatus ? '已支�? : ' 未支�? %>
                                                    </small>
                                                    <div class="mt-1">
                                                      <small class="text-primary fw-bold">¥<%=
                                                          parseFloat(order.deposit_amount || 0).toFixed(2) %></small>
                                                    </div>
                                                    <% } else { %>
                                                      <!-- 写手显示：管理员设置了金额则显示已支付，否则显示确认状�?-->
                                                      <% const depositHasAmount=parseFloat(order.deposit_amount || 0)>
                                                        0;
                                                        const depositStatus = depositHasAmount ||
                                                        order.deposit_paid_status;
                                                        %>
                                                        <div
                                                          class="<%= depositStatus ? 'text-success' : 'text-muted' %> fs-4">
                                                          <i
                                                            class="fas fa-<%= depositStatus ? 'check-circle' : 'times-circle' %>"></i>
                                                        </div>
                                                        <small
                                                          class="<%= depositStatus ? 'text-success' : 'text-muted' %>">
                                                          <%= depositStatus ? '已支�? : ' 未支�? %>
                                                        </small>
                                                        <% } %>
                                              </div>
                                            </div>

                                            <!-- 尾款状�?-->
                                            <div
                                              class="d-flex justify-content-between align-items-center mb-3 p-3 bg-light rounded">
                                              <div>
                                                <h6 class="mb-1">尾款支付</h6>
                                                <small class="text-muted">Final Payment</small>
                                              </div>
                                              <div class="text-center">
                                                <% if (user.role==='admin' ) { %>
                                                  <!-- 管理员显示基于滑块状态，与写手保持一�?-->
                                                  <% const adminFinalHasAmount=parseFloat(order.final_amount || 0)> 0;
                                                    const adminFinalStatus = adminFinalHasAmount ||
                                                    order.final_paid_status;
                                                    %>
                                                    <div
                                                      class="<%= adminFinalStatus ? 'text-success' : 'text-muted' %> fs-4">
                                                      <i
                                                        class="fas fa-<%= adminFinalStatus ? 'check-circle' : 'times-circle' %>"></i>
                                                    </div>
                                                    <small
                                                      class="<%= adminFinalStatus ? 'text-success' : 'text-muted' %>">
                                                      <%= adminFinalStatus ? '已支�? : ' 未支�? %>
                                                    </small>
                                                    <div class="mt-1">
                                                      <small class="text-primary fw-bold">¥<%=
                                                          parseFloat(order.final_amount || 0).toFixed(2) %></small>
                                                    </div>
                                                    <% } else { %>
                                                      <!-- 写手显示：管理员设置了金额则显示已支付，否则显示确认状�?-->
                                                      <% const finalHasAmount=parseFloat(order.final_amount || 0)> 0;
                                                        const finalStatus = finalHasAmount || order.final_paid_status;
                                                        %>
                                                        <div
                                                          class="<%= finalStatus ? 'text-success' : 'text-muted' %> fs-4">
                                                          <i
                                                            class="fas fa-<%= finalStatus ? 'check-circle' : 'times-circle' %>"></i>
                                                        </div>
                                                        <small
                                                          class="<%= finalStatus ? 'text-success' : 'text-muted' %>">
                                                          <%= finalStatus ? '已支�? : ' 未支�? %>
                                                        </small>
                                                        <% } %>
                                              </div>
                                            </div>

                                            <!-- 佣金信息 -->
                                            <div class="text-center p-3 bg-primary bg-opacity-10 rounded">
                                              <h5 class="text-primary mb-1">¥<%= order.commission_amount || 0 %>
                                              </h5>
                                              <small class="text-muted">写手佣金</small>
                                            </div>

                                            <!-- 备注信息 -->
                                            <% if (order.payment_note && order.payment_note.trim() !=='' ) { %>
                                              <div class="mt-3 p-2 bg-light rounded">
                                                <small class="text-muted d-block mb-1">备注:</small>
                                                <div class="text-dark">
                                                  <%= order.payment_note %>
                                                </div>
                                              </div>
                                              <% } %>

                                          </div>

                                          <!-- 操作按钮 -->
                                          <% if (user.role==='admin' || (user.role==='writer' &&
                                            order.writer_id===user.id)) { %>
                                            <div class="d-grid gap-2">
                                              <% if (user.role==='admin' ) { %>
                                                <button class="btn btn-outline-primary btn-sm"
                                                  onclick="showPaymentModal()">
                                                  <i class="fas fa-edit me-1"></i>编辑支付信息
                                                </button>
                                                <button class="btn btn-outline-info btn-sm"
                                                  onclick="showCommissionModal()">
                                                  <i class="fas fa-coins me-1"></i>设置佣金
                                                </button>
                                                <% } else if (user.role==='writer' && order.writer_id===user.id) { %>
                                                  <button class="btn btn-outline-primary btn-sm"
                                                    onclick="showWriterPaymentModal()">
                                                    <i class="fas fa-check-circle me-1"></i>更新支付状�? </button>
                                                  <% } %>
                                            </div>
                                            <% } %>
                                        </div>

                                        <!-- 二维�?-->
                                        <% if (order.qr_code_path) { %>
                                          <div class="card">
                                            <div class="card-header">
                                              <h5 class="mb-0"><i class="fas fa-qrcode me-2"></i>订单二维�?/h5>
                                            </div>
                                            <div class="card-body text-center">
                                              <img src="<%= order.qr_code_path %>" alt="订单二维�? class=" img-fluid mb-2"
                                                style="max-width: 200px;">
                                              <p class="text-muted small">扫描查看订单状�?/p>
                                            </div>
                                          </div>
                                          <% } %>
                                      </div>
                                    </div>



                                    <!-- 支付信息编辑模态框 -->
                                    <% if (user.role==='admin' ) { %>
                                      <div class="modal fade" id="paymentModal" tabindex="-1">
                                        <div class="modal-dialog">
                                          <div class="modal-content">
                                            <div class="modal-header">
                                              <h5 class="modal-title">编辑支付信息</h5>
                                              <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                                            </div>
                                            <form id="paymentForm" method="POST"
                                              action="/orders/<%= order.id %>/payment">
                                              <div class="modal-body">
                                                <div class="mb-3">
                                                  <label class="form-label">定金金额</label>
                                                  <input type="number" class="form-control" name="deposit_amount"
                                                    step="0.01" min="0" value="<%= order.deposit_amount || 0 %>">
                                                </div>
                                                <div class="mb-3">
                                                  <label class="form-label">尾款金额</label>
                                                  <input type="number" class="form-control" name="final_amount"
                                                    step="0.01" min="0" value="<%= order.final_amount || 0 %>">
                                                </div>
                                                <div class="mb-3">
                                                  <label class="form-label">其他金额</label>
                                                  <input type="number" class="form-control" name="other_amount"
                                                    step="0.01" min="0" value="<%= order.other_amount || 0 %>">
                                                </div>

                                                <!-- 支付状态确�?-->
                                                <div class="mb-3">
                                                  <label class="form-label">支付状态确�?/label>

                                                    <!-- 定金状�?-->
                                                    <div class="mb-3">
                                                      <label class="form-label">定金支付状�?/label>
                                                        <div class="form-check form-switch">
                                                          <% const depositHasAmount=parseFloat(order.deposit_amount ||
                                                            0)> 0;
                                                            const depositChecked = depositHasAmount ||
                                                            order.deposit_paid_status;
                                                            %>
                                                            <input class="form-check-input" type="checkbox"
                                                              id="adminDepositPaid" name="deposit_paid_status"
                                                              <%=depositChecked ? 'checked' : '' %>>
                                                            <label class="form-check-label" for="adminDepositPaid">
                                                              已收到定�? </label>
                                                        </div>
                                                    </div>

                                                    <!-- 尾款状�?-->
                                                    <div class="mb-3">
                                                      <label class="form-label">尾款支付状�?/label>
                                                        <div class="form-check form-switch">
                                                          <% const finalHasAmount=parseFloat(order.final_amount || 0)>
                                                            0;
                                                            const finalChecked = finalHasAmount ||
                                                            order.final_paid_status;
                                                            %>
                                                            <input class="form-check-input" type="checkbox"
                                                              id="adminFinalPaid" name="final_paid_status"
                                                              <%=finalChecked ? 'checked' : '' %>>
                                                            <label class="form-check-label" for="adminFinalPaid">
                                                              已收到尾�? </label>
                                                        </div>
                                                    </div>
                                                </div>

                                                <div class="mb-3">
                                                  <label class="form-label">备注信息</label>
                                                  <textarea class="form-control" name="payment_note" rows="3"
                                                    placeholder="请输入支付相关备注信�?><%= order.payment_note || '' %></textarea>
                          </div>
                        </div>
                        <div class=" modal-footer">
                          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                          <button type="submit" class="btn btn-primary">保存</button>
                        </div>
                      </form>
                    </div>
                  </div>
                </div>
                <% } %>

                  <!-- 二维码模态框 -->
                  <div class="modal fade" id="qrModal" tabindex="-1">
                    <div class="modal-dialog modal-dialog-centered">
                      <div class="modal-content">
                        <div class="modal-header">
                          <h5 class="modal-title">订单二维�?/h5>
                          <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body text-center">
                          <img id="qrImage" src="" alt="订单二维�? class="img-fluid mb-3">
                          <p class="text-muted">扫描二维码查看订单状�?/p>
                          <p class="fw-bold" id="qrOrderNo"></p>
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- 佣金设置模态框 -->
                  <% if (user.role==='admin' ) { %>
                    <div class="modal fade" id="commissionModal" tabindex="-1">
                      <div class="modal-dialog">
                        <div class="modal-content">
                          <form action="/orders/<%= order.id %>/commission" method="POST">
                            <div class="modal-header">
                              <h5 class="modal-title">设置佣金</h5>
                              <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                            </div>
                            <div class="modal-body">
                              <div class="mb-3">
                                <label class="form-label">佣金金额 (¥)</label>
                                <input type="number" class="form-control" name="commission_amount"
                                  value="<%= order.commission_amount %>" min="0" step="0.01" required>
                                <div class="form-text">当前支付总金额：¥<%= (parseFloat(order.deposit_amount || 0) +
                                    parseFloat(order.final_amount || 0) + parseFloat(order.other_amount ||
                                    0)).toFixed(2) %>
                                </div>
                              </div>
                            </div>
                            <div class="modal-footer">
                              <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                              <button type="submit" class="btn btn-primary">保存</button>
                            </div>
                          </form>
                        </div>
                      </div>
                    </div>

                    <!-- 追加佣金模态框 -->
                    <div class="modal fade" id="addCommissionModal" tabindex="-1">
                      <div class="modal-dialog">
                        <div class="modal-content">
                          <form action="/orders/<%= order.id %>/add-commission" method="POST">
                            <div class="modal-header">
                              <h5 class="modal-title">追加佣金</h5>
                              <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                            </div>
                            <div class="modal-body">
                              <div class="alert alert-info">
                                <i class="fas fa-info-circle me-2"></i>
                                当前佣金：�?%= order.commission_amount %>
                              </div>
                              <div class="mb-3">
                                <label class="form-label">追加金额 (¥)</label>
                                <input type="number" class="form-control" name="additional_amount" min="0.01"
                                  step="0.01" placeholder="0.00" required>
                                <div class="form-text">输入要追加的佣金金额</div>
                              </div>
                              <div class="mb-3">
                                <label class="form-label">追加原因</label>
                                <textarea class="form-control" name="reason" rows="3" placeholder="请说明追加佣金的原因..."
                                  required></textarea>
                                                </div>
                                              </div>
                                              <div class="modal-footer">
                                                <button type="button" class="btn btn-secondary"
                                                  data-bs-dismiss="modal">取消</button>
                                                <button type="submit" class="btn btn-success">确认追加</button>
                                              </div>
                                            </form>
                                          </div>
                                        </div>
                                      </div>
                                      <% } %>

                                        <!-- 写手支付状态更新模态框 -->
                                        <% if (user.role==='writer' && order.writer_id===user.id) { %>
                                          <div class="modal fade" id="writerPaymentModal" tabindex="-1">
                                            <div class="modal-dialog">
                                              <div class="modal-content">
                                                <div class="modal-header">
                                                  <h5 class="modal-title">更新支付状�?/h5>
                                                    <button type="button" class="btn-close"
                                                      data-bs-dismiss="modal"></button>
                                                </div>
                                                <form id="writerPaymentForm" method="POST"
                                                  action="/orders/<%= order.id %>/writer-payment">
                                                  <div class="modal-body">
                                                    <div class="alert alert-info">
                                                      <i class="fas fa-info-circle me-2"></i>
                                                      <strong>说明�?/strong>您只能更新支付状态，不能修改金额。请根据客户实际支付情况更新状态�?
                                                    </div>

                                                    <!-- 定金状�?-->
                                                    <div class="mb-3">
                                                      <label class="form-label">定金支付状�?/label>
                                                        <div class="form-check form-switch">
                                                          <% const depositHasAmount=parseFloat(order.deposit_amount ||
                                                            0)> 0;
                                                            const depositChecked = depositHasAmount ||
                                                            order.deposit_paid_status;
                                                            %>
                                                            <input class="form-check-input" type="checkbox"
                                                              id="depositPaid" name="deposit_paid" <%=depositChecked
                                                              ? 'checked' : '' %>>
                                                            <label class="form-check-label" for="depositPaid">
                                                              已收到定�? </label>
                                                        </div>
                                                    </div>

                                                    <!-- 尾款状�?-->
                                                    <div class="mb-3">
                                                      <label class="form-label">尾款支付状�?/label>
                                                        <div class="form-check form-switch">
                                                          <% const finalHasAmount=parseFloat(order.final_amount || 0)>
                                                            0;
                                                            const finalChecked = finalHasAmount ||
                                                            order.final_paid_status;
                                                            %>
                                                            <input class="form-check-input" type="checkbox"
                                                              id="finalPaid" name="final_paid" <%=finalChecked
                                                              ? 'checked' : '' %>>
                                                            <label class="form-check-label" for="finalPaid">
                                                              已收到尾�? </label>
                                                        </div>
                                                    </div>

                                                    <!-- 备注 -->
                                                    <div class="mb-3">
                                                      <label for="paymentNote" class="form-label">备注说明</label>
                                                      <textarea class="form-control" id="paymentNote"
                                                        name="payment_note" rows="3" maxlength="500"
                                                        placeholder="请说明支付情况或其他备注信息..."><%= order.payment_note || '' %></textarea>
                                                      <div class="form-text">最�?00个字�?/div>
                                                      </div>
                                                    </div>
                                                    <div class="modal-footer">
                                                      <button type="button" class="btn btn-secondary"
                                                        data-bs-dismiss="modal">取消</button>
                                                      <button type="submit" class="btn btn-primary">更新状�?/button>
                                                    </div>
                                                </form>
                                              </div>
                                            </div>
                                          </div>
                                          <% } %>

                                            <!-- 自定义样�?-->
                                            <style>
                                              .timeline {
                                                position: relative;
                                                padding-left: 30px;
                                              }

                                              .timeline::before {
                                                content: '';
                                                position: absolute;
                                                left: 15px;
                                                top: 0;
                                                bottom: 0;
                                                width: 2px;
                                                background: #dee2e6;
                                              }

                                              .timeline-item {
                                                position: relative;
                                                margin-bottom: 20px;
                                              }

                                              .timeline-item:last-child {
                                                margin-bottom: 0;
                                              }

                                              .timeline-marker {
                                                position: absolute;
                                                left: -22px;
                                                top: 5px;
                                                width: 16px;
                                                height: 16px;
                                                background: white;
                                                border-radius: 50%;
                                                display: flex;
                                                align-items: center;
                                                justify-content: center;
                                                z-index: 1;
                                              }

                                              .timeline-marker i {
                                                font-size: 8px;
                                              }

                                              .timeline-content {
                                                background: #f8f9fa;
                                                border: 1px solid #dee2e6;
                                                border-radius: 8px;
                                                padding: 15px;
                                                margin-left: 10px;
                                              }

                                              .timeline-item-current .timeline-content {
                                                background: #e3f2fd;
                                                border-color: #2196f3;
                                              }

                                              .timeline-item-current .timeline-marker {
                                                background: #2196f3;
                                                color: white;
                                              }
                                            </style>

                                            <script>
                                              function showQRCode(qrPath, orderNo) {
                                                document.getElementById('qrImage').src = qrPath;
                                                document.getElementById('qrOrderNo').textContent = '订单�? ' + orderNo;
                                                new bootstrap.Modal(document.getElementById('qrModal')).show();
                                              }

                                              function showPaymentModal() {
                                                new bootstrap.Modal(document.getElementById('paymentModal')).show();
                                              }



                                              function showCommissionModal() {
                                                new bootstrap.Modal(document.getElementById('commissionModal')).show();
                                              }

                                              function showAddCommissionModal() {
                                                new bootstrap.Modal(document.getElementById('addCommissionModal')).show();
                                              }

                                              // 显示写手支付状态模态框
                                              function showWriterPaymentModal() {
                                                new bootstrap.Modal(document.getElementById('writerPaymentModal')).show();
                                              }

                                              // 复制订单号功�?                            function copyOrderNo() {
                                              const orderNo = document.getElementById('orderNo').textContent.trim();

                                              // 直接使用降级方案，更兼容
                                              fallbackCopyTextToClipboard(orderNo);
                            }

                                              // 降级复制方案
                                              function fallbackCopyTextToClipboard(text) {
                                                const textArea = document.createElement("textarea");
                                                textArea.value = text;
                                                textArea.style.position = "fixed";
                                                textArea.style.top = "-9999px";
                                                textArea.style.left = "-9999px";
                                                textArea.style.opacity = "0";
                                                document.body.appendChild(textArea);
                                                textArea.select();
                                                textArea.setSelectionRange(0, 99999); // 兼容移动设备

                                                try {
                                                  const successful = document.execCommand('copy');
                                                  if (successful) {
                                                    showCopySuccess();
                                                  } else {
                                                    throw new Error('复制命令执行失败');
                                                  }
                                                } catch (err) {
                                                  console.error('复制失败:', err);
                                                  // 显示订单号让用户手动复制
                                                  prompt('复制失败，请手动复制订单�?', text);
                                                }

                                                document.body.removeChild(textArea);
                                              }

                                              // 显示复制成功提示
                                              function showCopySuccess() {
                                                const button = event.target.closest('button');
                                                const originalHtml = button.innerHTML;
                                                button.innerHTML = '<i class="fas fa-check text-success"></i>';
                                                button.classList.add('btn-success');
                                                button.classList.remove('btn-outline-secondary');

                                                setTimeout(() => {
                                                  button.innerHTML = originalHtml;
                                                  button.classList.remove('btn-success');
                                                  button.classList.add('btn-outline-secondary');
                                                }, 1500);
                                              }

                                              // 取消代部署任�?                            function cancelDeploymentTask(taskId) {
                                              if (confirm('确定要取消代部署任务吗？任务将从任务大厅移除，扣除的佣金将退回�?)) {
                                fetch(`/orders/deployment/${taskId}/cancel`, {
                                                method: 'POST',
                                                headers: {
                                                  'Content-Type': 'application/json',
                                                  'Accept': 'application/json'
                                                }
                                              })
                                                  .then(response => response.json())
                                                  .then(data => {
                                                    if (data.success) {
                                                      alert('代部署任务已取消');
                                                      location.reload();
                                                    } else {
                                                      alert('取消代部署任务失败：' + data.message);
                                                    }
                                                  })
                                                  .catch(error => {
                                                    console.error('取消代部署任务错�?', error);
                                                    alert('取消代部署任务失败，请稍后重�?);
                                  });
                              }
                            }

                                              // 删除订单
                                              function deleteOrder(orderId) {
                                                if (confirm('确定要删除这个订单吗？此操作不可恢复，相关的任务状态也会被重置�?)) {
                                fetch(`/orders/${orderId}/delete`, {
                                                  method: 'POST',
                                                  headers: {
                                                    'Content-Type': 'application/json',
                                                    'Accept': 'application/json',
                                                    'X-Requested-With': 'XMLHttpRequest'
                                                  }
                                                })
                                                    .then(response => response.json())
                                                    .then(data => {
                                                      if (data.success) {
                                                        alert('订单删除成功');
                                                        window.location.href = '/orders';
                                                      } else {
                                                        alert('删除失败: ' + data.message);
                                                      }
                                                    })
                                                    .catch(error => {
                                                      console.error('删除订单错误:', error);
                                                      alert('删除失败，请稍后重试');
                                                    });
                                              }
                            }

                                              // 状态更新表单提�?                            document.getElementById('statusForm').addEventListener('submit', function (e) {
                                              const status = document.getElementById('statusSelect').value;
                                              if (!status) {
                                                e.preventDefault();
                                                alert('请选择要更新的状�?);
                                return;
                                              }

                                              if (!confirm('确定要更新订单状态吗�?)) {
                                e.preventDefault();
                              }
                            });

                                              // 转单确认
                                              function confirmTransfer() {
                                                const reason = document.getElementById('transferReason').value.trim();
                                                let message = '确定要将此订单转到任务大厅吗？\n\n';
                                                message += '转单后：\n';
                                                message += '�?订单将在任务大厅显示3小时\n';
                                                message += '�?其他写手可以接单\n';
                                                message += '�?超时后自动退回给您\n';

                                                if (reason) {
                                                  message += '\n转单原因�? + reason;
                                                }

                                                if (confirm(message)) {
                                                  document.getElementById('transferForm').submit();
                                                }
                                              }

                                              // 取消转单
                                              function cancelTransfer() {
                                                if (confirm('确定要取消转单吗？订单将立即退回给您�?)) {
                                // 需要先获取转单ID，这里简化处理，通过订单ID查找
                                fetch(`/orders/<%= order.id %>/transfer-history`)
                                                    .then(response => response.json())
                                                    .then(data => {
                                                      if (data.success && data.transfers.length > 0) {
                                                        const pendingTransfer = data.transfers.find(t => t.status === 'pending');
                                                        if (pendingTransfer) {
                                                          return fetch(`/orders/transfer/${pendingTransfer.id}/cancel`, {
                                                            method: 'POST',
                                                            headers: {
                                                              'Content-Type': 'application/json',
                                                              'Accept': 'application/json'
                                                            }
                                                          });
                                                        } else {
                                                          throw new Error('没有找到待处理的转单记录');
                                                        }
                                                      } else {
                                                        throw new Error('获取转单历史失败');
                                                      }
                                                    })
                                                    .then(response => response.json())
                                                    .then(data => {
                                                      if (data.success) {
                                                        alert('转单已取�?);
                                      location.reload();
                                                      } else {
                                                        alert('取消转单失败�? + data.message);
                                    }
                                                    })
                                                    .catch(error => {
                                                      console.error('取消转单错误:', error);
                                                      alert('取消转单失败，请稍后重试');
                                                    });
                                              }
                            }

                                              // 代部署确�?                            function confirmCreateDeployment() {
                                              const description = document.getElementById('deploymentDescription').value.trim();
                                              const sourceCodeFile = document.getElementById('sourceCodeFile').files[0];

                                              let message = '确定要创建代部署任务吗？\n\n';
                                              message += '代部署说明：\n';
                                              message += '�?代部署佣金：10元（从您的订单佣金中扣除）\n';
                                              message += '�?任务将发布到任务大厅，长期有效\n';
                                              message += '�?部署完成后，10元将自动转给部署写手\n';

                                              if (description) {
                                                message += '\n部署说明�? + description;
                                              }

                                              if (sourceCodeFile) {
                                                message += '\n源代码文件：' + sourceCodeFile.name;
                                              }

                                              if (confirm(message)) {
                                                // 如果有文件上传，显示上传进度
                                                if (sourceCodeFile) {
                                                  const submitBtn = document.querySelector('#deploymentForm button[type="submit"]');
                                                  if (submitBtn) {
                                                    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>创建�?..';
                                                    submitBtn.disabled = true;
                                                  }
                                                }
                                                document.getElementById('deploymentForm').submit();
                                              }
                            }

                            // 加载代部署任务信�?                            function loadDeploymentTaskInfo() {
                              // 总是尝试加载代部署任务信息，让后端判断是否存�?                              fetch(`/orders/<%= order.id %>/deployment`)
                                .then(response => response.json())
                                                .then(data => {
                                                  if (data.success && data.deploymentTask) {
                                                    const task = data.deploymentTask;
                                                    const infoDiv = document.getElementById('deploymentTaskInfo');

                                                    // 更新进度�?                                    updateDeploymentProgress(task.status);

                                                    let html = '';
                                                    if (task.status === 'pending') {
                                                      html = `
                                      <div class="alert alert-info mb-3">
                                        <i class="fas fa-info-circle me-2"></i>
                                        <strong>代部署任务已发布到任务大厅，等待其他写手接单</strong>
                                      </div>
                                      <div class="mb-2">
                                        <strong>部署佣金�?/strong>¥${task.formatted_deployment_commission}
                                      </div>
                                      <div class="mb-2">
                                        <strong>发布时间�?/strong>${task.formatted_created_at}
                                      </div>
                                      ${task.deployment_description ? `
                                        <div class="mb-3">
                                          <strong>部署说明�?/strong><br>
                                          <div class="text-muted small">${task.deployment_description}</div>
                                        </div>
                                      ` : ''}

                                      <div class="mt-3">
                                        <button type="button" class="btn btn-outline-danger btn-sm" onclick="cancelDeploymentTask(${task.id})">
                                          <i class="fas fa-times me-2"></i>取消代部署任�?                                        </button>
                                      </div>

                                      <!-- 源码文件管理 -->
                                      <div class="mb-3">
                                        <strong>源码文件�?/strong>
                                        <div id="sourceCodeSection-${task.id}">
                                          ${task.source_code_file ? `
                                            <div class="d-flex align-items-center gap-2 mt-2">
                                              <i class="fas fa-file-archive text-primary"></i>
                                              <span>${task.source_code_filename || '源码文件'}</span>
                                              <a href="/files/download/${task.source_code_file_id || ''}" class="btn btn-sm btn-outline-primary">
                                                <i class="fas fa-download me-1"></i>下载
                                              </a>
                                            </div>
                                            <div class="text-muted small mt-1">
                                              上传时间�?{task.source_code_uploaded_at || '未知'}
                                            </div>
                                          ` : `
                                            <div class="text-muted small mt-2">暂无源码文件</div>
                                          `}
                                        </div>

                                        <!-- 上传源码文件表单 -->
                                        <div class="mt-2">
                                          <form id="sourceCodeUploadForm-${task.id}" enctype="multipart/form-data" style="display: none;">
                                            <div class="input-group input-group-sm">
                                              <input type="file" class="form-control" name="sourceCode"
                                                     accept=".zip,.rar,.7z,.tar,.gz,.txt,.js,.html,.css,.json,.md">
                                              <button type="button" class="btn btn-primary" onclick="uploadSourceCode(${task.id})">
                                                <i class="fas fa-upload me-1"></i>上传
                                              </button>
                                              <button type="button" class="btn btn-outline-secondary" onclick="toggleSourceCodeUpload(${task.id})">
                                                取消
                                              </button>
                                            </div>
                                            <div class="form-text">支持压缩包（ZIP、RAR�?Z等）或代码文件，最�?0MB</div>
                                          </form>
                                          <button type="button" class="btn btn-sm btn-outline-primary mt-1" onclick="toggleSourceCodeUpload(${task.id})">
                                            <i class="fas fa-upload me-1"></i>${task.source_code_file ? '更新' : '上传'}源码文件
                                          </button>
                                        </div>
                                      </div>
                                    `;
                                                    } else if (task.status === 'accepted') {
                                                      const isDeploymentWriter = <%= user.id %> === task.deployment_writer_id;
                                                      html = `
                                      <div class="alert alert-success mb-3">
                                        <i class="fas fa-check-circle me-2"></i>
                                        <strong>代部署任务已被接受，正在进行部署</strong>
                                      </div>
                                      <div class="mb-2">
                                        <strong>部署写手�?/strong>${task.deployment_writer_name || task.deployment_writer_real_name || '未知'}
                                      </div>
                                      <div class="mb-2">
                                        <strong>接受时间�?/strong>${task.formatted_accepted_at}
                                      </div>
                                      <div class="mb-2">
                                        <strong>部署佣金�?/strong>¥${task.formatted_deployment_commission}
                                      </div>
                                      ${task.deployment_description ? `
                                        <div class="mb-3">
                                          <strong>部署说明�?/strong><br>
                                          <div class="text-muted small">${task.deployment_description}</div>
                                        </div>
                                      ` : ''}

                                      <!-- 源码文件管理 -->
                                      <div class="mb-3">
                                        <strong>源码文件�?/strong>
                                        <div id="sourceCodeSection-${task.id}">
                                          ${task.source_code_file ? `
                                            <div class="d-flex align-items-center gap-2 mt-2">
                                              <i class="fas fa-file-archive text-primary"></i>
                                              <span>${task.source_code_filename || '源码文件'}</span>
                                              <a href="/files/download/${task.source_code_file_id || ''}" class="btn btn-sm btn-outline-primary">
                                                <i class="fas fa-download me-1"></i>下载
                                              </a>
                                            </div>
                                            <div class="text-muted small mt-1">
                                              上传时间�?{task.source_code_uploaded_at || '未知'}
                                            </div>
                                          ` : `
                                            <div class="text-muted small mt-2">暂无源码文件</div>
                                          `}
                                        </div>

                                        <!-- 上传源码文件表单（只有原写手和部署写手可以上传） -->
                                        ${(<%= user.id %> === task.original_writer_id || <%= user.id %> === task.deployment_writer_id) ? `
                                          <div class="mt-2">
                                            <form id="sourceCodeUploadForm-${task.id}" enctype="multipart/form-data" style="display: none;">
                                              <div class="input-group input-group-sm">
                                                <input type="file" class="form-control" name="sourceCode"
                                                       accept=".zip,.rar,.7z,.tar,.gz,.txt,.js,.html,.css,.json,.md">
                                                <button type="button" class="btn btn-primary" onclick="uploadSourceCode(${task.id})">
                                                  <i class="fas fa-upload me-1"></i>上传
                                                </button>
                                                <button type="button" class="btn btn-outline-secondary" onclick="toggleSourceCodeUpload(${task.id})">
                                                  取消
                                                </button>
                                              </div>
                                              <div class="form-text">支持压缩包（ZIP、RAR�?Z等）或代码文件，最�?0MB</div>
                                            </form>
                                            <button type="button" class="btn btn-sm btn-outline-primary mt-1" onclick="toggleSourceCodeUpload(${task.id})">
                                              <i class="fas fa-upload me-1"></i>${task.source_code_file ? '更新' : '上传'}源码文件
                                            </button>
                                          </div>
                                        ` : ''
                            }
                                      </div >

                                                ${
                                                isDeploymentWriter ? `
                                        <div class="d-flex gap-2 flex-wrap">
                                          <button type="button" class="btn btn-success btn-sm" onclick="completeDeploymentTask(${task.id})">
                                            <i class="fas fa-check me-2"></i>完成部署任务
                                          </button>
                                          <button type="button" class="btn btn-warning btn-sm" onclick="showTransferDeploymentModal(${task.id})">
                                            <i class="fas fa-exchange-alt me-2"></i>遇忙转让
                                          </button>
                                        </div>
                                      ` : ''
                                              }
                                              `;
                                  } else if (task.status === 'completed') {
                                    html = `
                                                < div class="alert alert-success mb-3" >
                                        <i class="fas fa-check-circle me-2"></i>
                                        <strong>代部署任务已完成</strong>
                                      </div >
                                      <div class="mb-2">
                                        <strong>部署写手�?/strong>${task.deployment_writer_name || task.deployment_writer_real_name || '未知'}
                                      </div>
                                      <div class="mb-2">
                                        <strong>完成时间�?/strong>${task.formatted_completed_at}
                                      </div>
                                      <div class="mb-2">
                                        <strong>部署佣金�?/strong>¥${task.formatted_deployment_commission}（已转给部署写手�?                                      </div>
                                              `;
                                  }

                                  infoDiv.innerHTML = html;
                                } else {
                                  document.getElementById('deploymentTaskInfo').innerHTML =
                                    '<div class="text-muted">暂无代部署任务信�?/div>';
                                }
                              })
                              .catch(error => {
                                console.error('加载代部署任务信息错�?', error);
                                document.getElementById('deploymentTaskInfo').innerHTML =
                                  '<div class="text-danger">加载代部署任务信息失�?/div>';
                              });
                            }



                            // 完成代部署任�?                            function completeDeploymentTask(taskId) {
                              if (confirm('确定已完成部署任务吗？完成后将获�?0元佣金，此操作不可撤销�?)) {
                                fetch('/orders/deployment/' + taskId + '/complete', {
                                  method: 'POST',
                                  headers: {
                                    'Content-Type': 'application/json',
                                    'Accept': 'application/json'
                                  }
                                })
                                  .then(response => response.json())
                                  .then(data => {
                                    if (data.success) {
                                      alert('代部署任务已完成，佣金已到账�?);
                                      location.reload();
                                    } else {
                                      alert('完成代部署任务失败：' + data.message);
                                    }
                                  })
                                  .catch(error => {
                                    console.error('完成代部署任务错�?', error);
                                    alert('完成代部署任务失败，请稍后重�?);
                                  });
                              }
                            }

                            // 上传微信群二维码
                            function uploadQRCode() {
                              const fileInput = document.getElementById('qrCodeFile');
                              const file = fileInput.files[0];

                              if (!file) {
                                alert('请选择要上传的二维码图�?);
                                return;
                              }

                              // 验证文件类型
                              const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
                              if (!allowedTypes.includes(file.type)) {
                                alert('只支持JPG、PNG、GIF、WebP格式的图�?);
                                return;
                              }

                              // 验证文件大小�?0MB�?                              if (file.size > 50 * 1024 * 1024) {
                                alert('文件大小不能超过50MB');
                                return;
                              }

                              const formData = new FormData();
                              formData.append('qrCode', file);

                              // 显示上传进度
                              const uploadBtn = event.target;
                              const originalText = uploadBtn.innerHTML;
                              uploadBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>上传�?..';
                              uploadBtn.disabled = true;

                              fetch(`/ files / upload / qr - code /<%= order.id %> `, {
                                method: 'POST',
                                headers: {
                                  'Accept': 'application/json'
                                },
                                body: formData
                              })
                                .then(response => response.json())
                                .then(data => {
                                  if (data.success) {
                                    alert('二维码上传成功！');
                                    location.reload();
                                  } else {
                                    alert('上传失败�? + data.message);
                                  }
                                })
                                .catch(error => {
                                  console.error('上传二维码错�?', error);
                                  alert('上传失败，请稍后重试');
                                })
                                .finally(() => {
                                  uploadBtn.innerHTML = originalText;
                                  uploadBtn.disabled = false;
                                });
                            }

                            // 切换源码文件上传表单显示
                            function toggleSourceCodeUpload(taskId) {
                              const form = document.getElementById(`sourceCodeUploadForm - ${ taskId } `);
                              if (form.style.display === 'none') {
                                form.style.display = 'block';
                              } else {
                                form.style.display = 'none';
                                // 清空文件选择
                                const fileInput = form.querySelector('input[type="file"]');
                                if (fileInput) {
                                  fileInput.value = '';
                                }
                              }
                            }

                            // 上传源码文件
                            function uploadSourceCode(taskId) {
                              const form = document.getElementById(`sourceCodeUploadForm - ${ taskId } `);
                              const fileInput = form.querySelector('input[type="file"]');
                              const file = fileInput.files[0];

                              if (!file) {
                                alert('请选择要上传的源码文件');
                                return;
                              }

                              // 验证文件大小�?0MB�?                              if (file.size > 50 * 1024 * 1024) {
                                alert('文件大小不能超过50MB');
                                return;
                              }

                              const formData = new FormData();
                              formData.append('sourceCode', file);

                              // 显示上传进度
                              const uploadBtn = event.target;
                              const originalText = uploadBtn.innerHTML;
                              uploadBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>上传�?..';
                              uploadBtn.disabled = true;

                              fetch(`/ files / upload / source - code / ${ taskId } `, {
                                method: 'POST',
                                headers: {
                                  'Accept': 'application/json'
                                },
                                body: formData
                              })
                                .then(response => response.json())
                                .then(data => {
                                  if (data.success) {
                                    alert('源码文件上传成功�?);
                                    // 重新加载代部署任务信�?                                    loadDeploymentTaskInfo();
                                    // 隐藏上传表单
                                    toggleSourceCodeUpload(taskId);
                                  } else {
                                    alert('上传失败�? + data.message);
                                  }
                                })
                                .catch(error => {
                                  console.error('上传源码文件错误:', error);
                                  alert('上传失败，请稍后重试');
                                })
                                .finally(() => {
                                  uploadBtn.innerHTML = originalText;
                                  uploadBtn.disabled = false;
                                });
                            }

                            // 更新代部署进度条
                            function updateDeploymentProgress(status) {
                              const statusBadge = document.getElementById('deploymentStatusBadge');
                              const progressBar = document.getElementById('deploymentProgressBar');

                              if (!statusBadge || !progressBar) return;

                              switch (status) {
                                case 'pending':
                                  statusBadge.textContent = '等待接单';
                                  statusBadge.className = 'badge bg-warning text-dark';
                                  progressBar.style.width = '33%';
                                  progressBar.className = 'progress-bar bg-warning';
                                  break;
                                case 'accepted':
                                  statusBadge.textContent = '进行�?;
                                  statusBadge.className = 'badge bg-info';
                                  progressBar.style.width = '66%';
                                  progressBar.className = 'progress-bar bg-info';
                                  break;
                                case 'completed':
                                  statusBadge.textContent = '已完�?;
                                  statusBadge.className = 'badge bg-success';
                                  progressBar.style.width = '100%';
                                  progressBar.className = 'progress-bar bg-success';
                                  break;
                                case 'cancelled':
                                  statusBadge.textContent = '已取�?;
                                  statusBadge.className = 'badge bg-secondary';
                                  progressBar.style.width = '0%';
                                  progressBar.className = 'progress-bar bg-secondary';
                                  break;
                              }
                            }

                            // 加载转单信息和倒计�?                            function loadTransferInfo() {
                          <% if (order.is_transferred && transferConfig && transferConfig.enabled) { %>
                                fetch(`/ orders /<%= order.id %>/transfer-history`)
                                  .then(response => response.json())
                                                .then(data => {
                                                  if (data.success && data.transfers.length > 0) {
                                                    const pendingTransfer = data.transfers.find(t => t.status === 'pending');
                                                    if (pendingTransfer) {
                                                      // 显示发布时间
                                                      const transferTimeElement = document.getElementById('transferTime');
                                                      if (transferTimeElement) {
                                                        transferTimeElement.textContent = pendingTransfer.formatted_created_at;
                                                      }

                                                      // 启动倒计�?                                    startTransferCountdown(pendingTransfer.created_at);
                                                    }
                                                  }
                                                })
                                                .catch(error => {
                                                  console.error('加载转单信息错误:', error);
                                                });
                          <% } %>
                        }

                                              // 转单倒计�?                            function startTransferCountdown(createdAt) {
                                              const countdownElement = document.getElementById('transferCountdown');
                                              if (!countdownElement) return;

                                              function updateCountdown() {
                                                const now = new Date();
                                                const created = new Date(createdAt);
                                                const expireTime = new Date(created.getTime() + 3 * 60 * 60 * 1000); // 3小时�?                                const remaining = expireTime - now;

                                                if (remaining <= 0) {
                                                  countdownElement.textContent = '已过�?;
                                                  countdownElement.className = 'text-danger fw-bold';
                                                  return;
                                                }

                                                const hours = Math.floor(remaining / (1000 * 60 * 60));
                                                const minutes = Math.floor((remaining % (1000 * 60 * 60)) / (1000 * 60));
                                                const seconds = Math.floor((remaining % (1000 * 60)) / 1000);

                                                countdownElement.textContent = `${hours}�?{minutes}�?{seconds}秒`;

                                                // 根据剩余时间改变颜色
                                                if (remaining < 30 * 60 * 1000) { // 少于30分钟
                                                  countdownElement.className = 'text-danger fw-bold';
                                                } else if (remaining < 60 * 60 * 1000) { // 少于1小时
                                                  countdownElement.className = 'text-warning fw-bold';
                                                } else {
                                                  countdownElement.className = 'text-info fw-bold';
                                                }
                                              }

                                              updateCountdown();
                                              setInterval(updateCountdown, 1000);
                            }

                                              // 显示转让代部署任务模态框
                                              function showTransferDeploymentModal(taskId) {
                                                window.currentTransferTaskId = taskId;
                                                new bootstrap.Modal(document.getElementById('transferDeploymentModal')).show();
                                              }

                                              // 确认转让代部署任�?                            function confirmTransferDeployment() {
                                              const taskId = window.currentTransferTaskId;
                                              const reason = document.getElementById('transferReason').value.trim();

                                              if (!taskId) {
                                                alert('任务ID错误');
                                                return;
                                              }

                                              let message = '确定要转让此代部署任务吗？\n\n';
                                              message += '转让后：\n';
                                              message += '�?任务将重新发布到任务大厅\n';
                                              message += '�?其他写手可以接单\n';
                                              message += '�?您将失去此任务的执行权\n';

                                              if (reason) {
                                                message += '\n转让原因�? + reason;
                                              }

                                              if (confirm(message)) {
                                                fetch(`/orders/deployment/${taskId}/transfer`, {
                                                  method: 'POST',
                                                  headers: {
                                                    'Content-Type': 'application/json',
                                                    'Accept': 'application/json'
                                                  },
                                                  body: JSON.stringify({ reason: reason })
                                                })
                                                  .then(response => response.json())
                                                  .then(data => {
                                                    if (data.success) {
                                                      alert('代部署任务已转让');
                                                      location.reload();
                                                    } else {
                                                      alert('转让失败�? + data.message);
                                    }
                                                  })
                                                  .catch(error => {
                                                    console.error('转让代部署任务错�?', error);
                                                    alert('转让失败，请稍后重试');
                                                  });
                                              }
                            }

                                              // 页面加载完成后加载信�?                            document.addEventListener('DOMContentLoaded', function () {
                                              loadDeploymentTaskInfo();
                                              loadTransferInfo();
                            });
                                            </script>

                                            <!-- 转让代部署任务模态框 -->
                                            <div class="modal fade" id="transferDeploymentModal" tabindex="-1">
                                              <div class="modal-dialog">
                                                <div class="modal-content">
                                                  <div class="modal-header">
                                                    <h5 class="modal-title">
                                                      <i class="fas fa-exchange-alt me-2"></i>转让代部署任�?
                                                    </h5>
                                                    <button type="button" class="btn-close"
                                                      data-bs-dismiss="modal"></button>
                                                  </div>
                                                  <div class="modal-body">
                                                    <div class="alert alert-warning">
                                                      <i class="fas fa-exclamation-triangle me-2"></i>
                                                      <strong>注意�?/strong>转让后任务将重新发布到任务大厅，等待其他写手接单。您将失去此任务的执行权�?
                                                    </div>

                                                    <form id="transferDeploymentForm">
                                                      <div class="mb-3">
                                                        <label for="transferReason" class="form-label">转让原因（可选）</label>
                                                        <textarea class="form-control" id="transferReason" name="reason"
                                                          rows="3" maxlength="500"
                                                          placeholder="请简要说明转让原�?.."></textarea>
                                                        <div class="form-text">最�?00个字�?/div>
                                                        </div>
                                                    </form>
                                                  </div>
                                                  <div class="modal-footer">
                                                    <button type="button" class="btn btn-secondary"
                                                      data-bs-dismiss="modal">取消</button>
                                                    <button type="button" class="btn btn-warning"
                                                      onclick="confirmTransferDeployment()">
                                                      <i class="fas fa-exchange-alt me-2"></i>确认转让
                                                    </button>
                                                  </div>
                                                </div>
                                              </div>
                                            </div>

                                            <!-- 编辑任务描述模态框 -->
                                            <div class="modal fade" id="editDescriptionModal" tabindex="-1">
                                              <div class="modal-dialog modal-lg">
                                                <div class="modal-content">
                                                  <div class="modal-header">
                                                    <h5 class="modal-title">编辑任务描述</h5>
                                                    <button type="button" class="btn-close"
                                                      data-bs-dismiss="modal"></button>
                                                  </div>
                                                  <div class="modal-body">
                                                    <form id="editDescriptionForm">
                                                      <div class="mb-3">
                                                        <label for="editDescription" class="form-label">任务描述</label>
                                                        <textarea class="form-control" id="editDescription"
                                                          name="description" rows="8"
                                                          placeholder="请详细描述任务的具体内容、目标和背景信息..."></textarea>
                                                        <div class="form-text">详细的描述有助于更好地理解任务需�?/div>
                                                        </div>
                                                    </form>
                                                  </div>
                                                  <div class="modal-footer">
                                                    <button type="button" class="btn btn-secondary"
                                                      data-bs-dismiss="modal">取消</button>
                                                    <button type="button" class="btn btn-primary"
                                                      onclick="saveTaskDescription()">
                                                      <i class="fas fa-save me-1"></i>保存
                                                    </button>
                                                  </div>
                                                </div>
                                              </div>
                                            </div>

                                            <!-- 编辑任务要求模态框 -->
                                            <div class="modal fade" id="editRequirementsModal" tabindex="-1">
                                              <div class="modal-dialog modal-lg">
                                                <div class="modal-content">
                                                  <div class="modal-header">
                                                    <h5 class="modal-title">编辑任务要求</h5>
                                                    <button type="button" class="btn-close"
                                                      data-bs-dismiss="modal"></button>
                                                  </div>
                                                  <div class="modal-body">
                                                    <form id="editRequirementsForm">
                                                      <div class="mb-3">
                                                        <label for="editRequirements" class="form-label">任务要求</label>
                                                        <textarea class="form-control" id="editRequirements"
                                                          name="requirements" rows="8"
                                                          placeholder="请列出具体的技术要求、交付标准、注意事项等..."></textarea>
                                                        <div class="form-text">明确的要求有助于确保任务质量</div>
                                                      </div>
                                                    </form>
                                                  </div>
                                                  <div class="modal-footer">
                                                    <button type="button" class="btn btn-secondary"
                                                      data-bs-dismiss="modal">取消</button>
                                                    <button type="button" class="btn btn-warning"
                                                      onclick="saveTaskRequirements()">
                                                      <i class="fas fa-save me-1"></i>保存
                                                    </button>
                                                  </div>
                                                </div>
                                              </div>
                                            </div>

                                            <script>
                                              // 编辑任务描述
                                              function editTaskDescription() {
                                                const currentDescription = document.getElementById('taskDescription').textContent.trim();
                                                const modal = new bootstrap.Modal(document.getElementById('editDescriptionModal'));

                                                // 设置当前值（如果不是"暂无任务描述"�?                              const textarea = document.getElementById('editDescription');
                                                if (currentDescription !== '暂无任务描述') {
                                                  textarea.value = currentDescription;
                                                } else {
                                                  textarea.value = '';
                                                }

                                                modal.show();
                                              }

                                              // 保存任务描述
                                              function saveTaskDescription() {
                                                const description = document.getElementById('editDescription').value.trim();
                              <% if (order.task && order.task.id) { %>
                              const taskId = <%= order.task.id %>;
                              <% } else { %>
                                                  console.error('任务ID不存�?);
                                alert('任务信息错误，无法保�?);
                                return;
                              <% } %>

                                                  fetch(`/tasks/${taskId}/update`, {
                                                    method: 'POST',
                                                    headers: {
                                                      'Content-Type': 'application/json',
                                                    },
                                                    body: JSON.stringify({ description: description })
                                                  })
                                                    .then(response => response.json())
                                                    .then(data => {
                                                      if (data.success) {
                                                        // 更新页面显示
                                                        document.getElementById('taskDescription').textContent = description || '暂无任务描述';
                                                        if (description) {
                                                          document.getElementById('taskDescription').className = 'mb-0';
                                                          document.getElementById('taskDescription').style.whiteSpace = 'pre-wrap';
                                                        } else {
                                                          document.getElementById('taskDescription').className = 'mb-0';
                                                        }

                                                        // 关闭模态框
                                                        const modal = bootstrap.Modal.getInstance(document.getElementById('editDescriptionModal'));
                                                        modal.hide();

                                                        // 显示成功消息
                                                        showSuccessMessage('任务描述更新成功');
                                                      } else {
                                                        alert('更新失败�? + data.message);
                                    }
                                                    })
                                                    .catch(error => {
                                                      console.error('Error:', error);
                                                      alert('更新失败，请重试');
                                                    });
                                              }

                                              // 编辑任务要求
                                              function editTaskRequirements() {
                                                const currentRequirements = document.getElementById('taskRequirements').textContent.trim();
                                                const modal = new bootstrap.Modal(document.getElementById('editRequirementsModal'));

                                                // 设置当前值（如果不是"暂无任务要求"�?                              const textarea = document.getElementById('editRequirements');
                                                if (currentRequirements !== '暂无任务要求') {
                                                  textarea.value = currentRequirements;
                                                } else {
                                                  textarea.value = '';
                                                }

                                                modal.show();
                                              }

                                              // 保存任务要求
                                              function saveTaskRequirements() {
                                                const requirements = document.getElementById('editRequirements').value.trim();
                                                const taskId = <%= order.task.id %>;

                                                fetch(`/tasks/${taskId}/update`, {
                                                  method: 'POST',
                                                  headers: {
                                                    'Content-Type': 'application/json',
                                                  },
                                                  body: JSON.stringify({ requirements: requirements })
                                                })
                                                  .then(response => response.json())
                                                  .then(data => {
                                                    if (data.success) {
                                                      // 更新页面显示
                                                      document.getElementById('taskRequirements').textContent = requirements || '暂无任务要求';
                                                      if (requirements) {
                                                        document.getElementById('taskRequirements').className = 'mb-0';
                                                        document.getElementById('taskRequirements').style.whiteSpace = 'pre-wrap';
                                                      } else {
                                                        document.getElementById('taskRequirements').className = 'mb-0';
                                                      }

                                                      // 关闭模态框
                                                      const modal = bootstrap.Modal.getInstance(document.getElementById('editRequirementsModal'));
                                                      modal.hide();

                                                      // 显示成功消息
                                                      showSuccessMessage('任务要求更新成功');
                                                    } else {
                                                      alert('更新失败�? + data.message);
                                  }
                                                  })
                                                  .catch(error => {
                                                    console.error('Error:', error);
                                                    alert('更新失败，请重试');
                                                  });
                                              }

                                              // 显示成功消息
                                              function showSuccessMessage(message) {
                                                // 创建成功提示
                                                const alertDiv = document.createElement('div');
                                                alertDiv.className = 'alert alert-success alert-dismissible fade show';
                                                alertDiv.innerHTML = `
    <i class="fas fa-check-circle me-2"></i>${message}
    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
  `;

                                                // 插入到页面顶�?                              const container = document.querySelector('.container-fluid') || document.body;
                                                container.insertBefore(alertDiv, container.firstChild);

                                                // 3秒后自动消失
                                                setTimeout(() => {
                                                  if (alertDiv.parentNode) {
                                                    alertDiv.remove();
                                                  }
                                                }, 3000);
                                              }
                                            </script>

                                            <%- include('../partials/footer') %>