{"name": "@jimp/custom", "version": "0.22.12", "description": "Interface to customize jimp configuration", "main": "dist/index.js", "module": "es/index.js", "repository": "jimp-dev/jimp", "types": "types/index.d.ts", "scripts": {"build": "npm run build:node:production && npm run build:module", "build:watch": "npm run build:node:debug -- -- --watch --verbose", "build:debug": "npm run build:node:debug", "build:module": "cross-env BABEL_ENV=module babel src -d es --source-maps --config-file ../../babel.config.js", "build:node": "babel src -d dist --source-maps --config-file ../../babel.config.js", "build:node:debug": "cross-env BABEL_ENV=development npm run build:node", "build:node:production": "cross-env BABEL_ENV=production npm run build:node"}, "author": "", "license": "MIT", "dependencies": {"@jimp/core": "^0.22.12"}, "publishConfig": {"access": "public"}, "gitHead": "a4a8d6364bbf97629749e196f3b0a4c94c9a7abc", "_resolved": "https://registry.npmmirror.com/@jimp/custom/-/custom-0.22.12.tgz", "_integrity": "sha512-xcmww1O/JFP2MrlGUMd3Q78S3Qu6W3mYTXYuIqFq33EorgYHV/HqymHfXy9GjiCJ7OI+7lWx6nYFOzU7M4rd1Q==", "_from": "@jimp/custom@^0.22.12"}