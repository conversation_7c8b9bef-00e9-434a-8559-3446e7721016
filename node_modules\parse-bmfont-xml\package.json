{"_from": "parse-bmfont-xml@^1.1.4", "_id": "parse-bmfont-xml@1.1.6", "_inBundle": false, "_integrity": "sha512-0c<PERSON>liVMZEhrFDwMh4SxIyVJpqYoOWDJ9P895tFuS+XuNzI5UBmBk5U5O4KuJdTnZpSBI4LFA2+ZiJaiwfSwlMA==", "_location": "/parse-bmfont-xml", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "parse-bmfont-xml@^1.1.4", "name": "parse-bmfont-xml", "escapedName": "parse-bmfont-xml", "rawSpec": "^1.1.4", "saveSpec": null, "fetchSpec": "^1.1.4"}, "_requiredBy": ["/load-bmfont"], "_resolved": "https://registry.npmmirror.com/parse-bmfont-xml/-/parse-bmfont-xml-1.1.6.tgz", "_shasum": "016b655da7aebe6da38c906aca16bf0415773767", "_spec": "parse-bmfont-xml@^1.1.4", "_where": "D:\\codes\\Godspeed\\Date\\2025-07-20 尚明伟2\\node_modules\\load-bmfont", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/mattdesl"}, "browser": "lib/browser.js", "bugs": {"url": "https://github.com/mattdesl/parse-bmfont-xml/issues"}, "bundleDependencies": false, "dependencies": {"xml-parse-from-string": "^1.0.0", "xml2js": "^0.5.0"}, "deprecated": false, "description": "parses XML BMFont files into a JavaScript object", "devDependencies": {"brfs": "^1.4.0", "browserify": "^9.0.3", "faucet": "0.0.1", "tape": "^3.5.0", "testling": "^1.7.1", "xhr": "^2.0.1"}, "homepage": "https://github.com/mattdesl/parse-bmfont-xml", "keywords": ["xml", "parse", "convert", "bmfont", "bm", "bitmap", "font", "bitmaps", "angel", "angelcode", "code", "text", "gl", "sprite", "sprites", "stackgl"], "license": "MIT", "main": "lib/index.js", "name": "parse-bmfont-xml", "repository": {"type": "git", "url": "git://github.com/mattdesl/parse-bmfont-xml.git"}, "scripts": {"test": "npm run test-node && npm run test-browser", "test-browser": "browserify test/test-browser.js -t brfs | testling | faucet", "test-node": "node test/test.js | faucet"}, "version": "1.1.6"}