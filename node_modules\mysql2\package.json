{"_from": "mysql2@^3.6.0", "_id": "mysql2@3.14.2", "_inBundle": false, "_integrity": "sha512-YD6mZMeoypmheHT6b2BrVmQFvouEpRICuvPIREulx2OvP1xAxxeqkMQqZSTBefv0PiOBKGYFa2zQtY+gf/4eQw==", "_location": "/mysql2", "_phantomChildren": {"safer-buffer": "2.1.2"}, "_requested": {"type": "range", "registry": true, "raw": "mysql2@^3.6.0", "name": "mysql2", "escapedName": "mysql2", "rawSpec": "^3.6.0", "saveSpec": null, "fetchSpec": "^3.6.0"}, "_requiredBy": ["#USER", "/"], "_resolved": "https://registry.npmmirror.com/mysql2/-/mysql2-3.14.2.tgz", "_shasum": "21fa725162da92d49261dce26362de539e11e5ca", "_spec": "mysql2@^3.6.0", "_where": "D:\\codes\\Godspeed\\Date\\2025-07-20 尚明伟2", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/sidorares/node-mysql2/issues"}, "bundleDependencies": false, "dependencies": {"aws-ssl-profiles": "^1.1.1", "denque": "^2.1.0", "generate-function": "^2.3.1", "iconv-lite": "^0.6.3", "long": "^5.2.1", "lru.min": "^1.0.0", "named-placeholders": "^1.1.3", "seq-queue": "^0.0.5", "sqlstring": "^2.3.2"}, "deprecated": false, "description": "fast mysql driver. Implements core protocol, prepared statements, ssl and compression in native JS", "devDependencies": {"@eslint/eslintrc": "^3.3.0", "@eslint/js": "^9.21.0", "@eslint/markdown": "^6.2.2", "@types/node": "^24.0.0", "@typescript-eslint/eslint-plugin": "^8.26.0", "@typescript-eslint/parser": "^8.26.0", "assert-diff": "^3.0.2", "benchmark": "^2.1.4", "c8": "^10.1.1", "error-stack-parser": "^2.0.3", "eslint-config-prettier": "^10.0.2", "eslint-plugin-async-await": "^0.0.0", "eslint-plugin-markdown": "^5.1.0", "eslint-plugin-prettier": "^5.2.3", "globals": "^16.0.0", "poku": "^3.0.0", "portfinder": "^1.0.28", "prettier": "^3.0.0", "typescript": "^5.0.2"}, "engines": {"node": ">= 8.0"}, "exports": {".": "./index.js", "./package.json": "./package.json", "./promise": "./promise.js", "./promise.js": "./promise.js"}, "files": ["lib", "typings/mysql", "index.js", "index.d.ts", "promise.js", "promise.d.ts"], "homepage": "https://sidorares.github.io/node-mysql2/docs", "keywords": ["mysql", "client", "server"], "license": "MIT", "main": "index.js", "name": "mysql2", "repository": {"type": "git", "url": "git+https://github.com/sidorares/node-mysql2.git"}, "scripts": {"benchmark": "node ./benchmarks/benchmark.js", "coverage-test": "c8 npm run test", "lint": "eslint . && prettier --check .", "lint:fix": "eslint . --fix && prettier --write .", "test": "poku -d -r=verbose --sequential test/esm test/unit test/integration", "test:bun": "bun poku -d --sequential test/esm test/unit test/integration", "test:deno": "deno run --allow-read --allow-env --allow-run npm:poku -d --sequential --denoAllow=\"read,env,net,sys\" test/esm test/unit test/integration", "test:tsc-build": "cd \"test/tsc-build\" && npx tsc -p \"tsconfig.json\"", "wait-port": "wait-on"}, "type": "commonjs", "typings": "typings/mysql/index", "version": "3.14.2"}