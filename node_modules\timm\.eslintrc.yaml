root: true
parser: '@typescript-eslint/parser'
plugins:
  - '@typescript-eslint'
extends:
  - eslint:recommended
  #  - plugin:react/recommended
  - plugin:@typescript-eslint/recommended
  - prettier
  - prettier/@typescript-eslint
env:
  node: true
  browser: true
  es6: true
  jest: true
rules:
  '@typescript-eslint/explicit-function-return-type': off
  '@typescript-eslint/explicit-member-accessibility': off
  '@typescript-eslint/indent': off
  '@typescript-eslint/member-delimiter-style': off
  '@typescript-eslint/no-explicit-any': off
  '@typescript-eslint/no-empty-function': off
  '@typescript-eslint/no-non-null-assertion': off
  '@typescript-eslint/no-var-requires': off
  '@typescript-eslint/explicit-module-boundary-types': off
  '@typescript-eslint/ban-ts-comment': off
  '@typescript-eslint/ban-types':
    - 2
    - types:
        Function: false
  '@typescript-eslint/no-unused-vars':
    - 2
    - argsIgnorePattern: '^_'
