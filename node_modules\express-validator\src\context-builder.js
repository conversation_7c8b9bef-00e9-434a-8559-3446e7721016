"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ContextBuilder = void 0;
const context_1 = require("./context");
class ContextBuilder {
    constructor() {
        this.stack = [];
        this.fields = [];
        this.locations = [];
        this.optional = false;
    }
    setFields(fields) {
        this.fields = fields;
        return this;
    }
    setLocations(locations) {
        this.locations = locations;
        return this;
    }
    setMessage(message) {
        this.message = message;
        return this;
    }
    addItem(...items) {
        this.stack.push(...items);
        return this;
    }
    setOptional(options) {
        this.optional = options;
        return this;
    }
    build() {
        return new context_1.Context(this.fields, this.locations, this.stack, this.optional, this.message);
    }
}
exports.ContextBuilder = ContextBuilder;
