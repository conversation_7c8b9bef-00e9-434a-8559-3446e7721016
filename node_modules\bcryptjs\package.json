{"_from": "bcryptjs", "_id": "bcryptjs@3.0.2", "_inBundle": false, "_integrity": "sha512-k38b3XOZKv60C4E2hVsXTolJWfkGRMbILBIe2IBITXciy5bOsTKot5kDrf3ZfufQtQOUN5mXceUEpU1rTl9Uog==", "_location": "/bcryptjs", "_phantomChildren": {}, "_requested": {"type": "tag", "registry": true, "raw": "bcryptjs", "name": "bcryptjs", "escapedName": "bcryptjs", "rawSpec": "", "saveSpec": null, "fetchSpec": "latest"}, "_requiredBy": ["#USER", "/"], "_resolved": "https://registry.npmmirror.com/bcryptjs/-/bcryptjs-3.0.2.tgz", "_shasum": "caadcca1afefe372ed6e20f86db8e8546361c1ca", "_spec": "bcryptjs", "_where": "D:\\codes\\Godspeed\\Date\\2025-07-20 尚明伟2", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bin": {"bcrypt": "bin/bcrypt"}, "browser": {"crypto": false}, "bugs": {"url": "https://github.com/dcodeIO/bcrypt.js/issues"}, "bundleDependencies": false, "contributors": [{"name": "<PERSON>", "email": "shaneG<PERSON><EMAIL>", "url": "https://github.com/shaneGirish"}, {"name": "<PERSON>", "url": "https://github.com/alexmurray"}, {"name": "<PERSON>", "url": "https://github.com/<PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/geekymole"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/nisa<PERSON>son"}], "deprecated": false, "description": "Optimized bcrypt in plain JavaScript with zero dependencies, with TypeScript support. Compatible to 'bcrypt'.", "devDependencies": {"bcrypt": "^5.1.1", "esm2umd": "^0.3.1", "prettier": "^3.5.0", "typescript": "^5.7.3"}, "exports": {".": {"import": {"types": "./index.d.ts", "default": "./index.js"}, "require": {"types": "./umd/index.d.ts", "default": "./umd/index.js"}}}, "files": ["index.js", "index.d.ts", "types.d.ts", "umd/index.js", "umd/index.d.ts", "umd/types.d.ts", "umd/package.json", "LICENSE", "README.md"], "homepage": "https://github.com/dcodeIO/bcrypt.js#readme", "keywords": ["bcrypt", "password", "auth", "authentication", "encryption", "crypt", "crypto"], "license": "BSD-3-<PERSON><PERSON>", "main": "umd/index.js", "name": "bcryptjs", "repository": {"type": "url", "url": "git+https://github.com/dcodeIO/bcrypt.js.git"}, "scripts": {"build": "node scripts/build.js", "format": "prettier --write .", "lint": "prettier --check .", "test": "npm run test:unit && npm run test:typescript", "test:typescript": "tsc --project tests/typescript/tsconfig.esnext.json && tsc --project tests/typescript/tsconfig.nodenext.json && tsc --project tests/typescript/tsconfig.commonjs.json && tsc --project tests/typescript/tsconfig.global.json", "test:unit": "node tests"}, "type": "module", "types": "umd/index.d.ts", "version": "3.0.2"}