{"_from": "yargs@^15.3.1", "_id": "yargs@15.4.1", "_inBundle": false, "_integrity": "sha512-aePbxDmcYW++PaqBsJ+HYUFwCdv4LVvdnhBy78E57PIor8/OVvhMrADFFEDh8DHDFRv/O9i3lPhsENjO7QX0+A==", "_location": "/yargs", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "yargs@^15.3.1", "name": "yargs", "escapedName": "yargs", "rawSpec": "^15.3.1", "saveSpec": null, "fetchSpec": "^15.3.1"}, "_requiredBy": ["/jest-runtime", "/jest/jest-cli", "/qrcode"], "_resolved": "https://registry.npmmirror.com/yargs/-/yargs-15.4.1.tgz", "_shasum": "0d87a16de01aee9d8bec2bfbf74f67851730f4f8", "_spec": "yargs@^15.3.1", "_where": "D:\\codes\\Godspeed\\Date\\2025-07-20 尚明伟2\\node_modules\\qrcode", "bugs": {"url": "https://github.com/yargs/yargs/issues"}, "bundleDependencies": false, "contributors": [{"name": "Yargs Contributors", "url": "https://github.com/yargs/yargs/graphs/contributors"}], "dependencies": {"cliui": "^6.0.0", "decamelize": "^1.2.0", "find-up": "^4.1.0", "get-caller-file": "^2.0.1", "require-directory": "^2.1.1", "require-main-filename": "^2.0.0", "set-blocking": "^2.0.0", "string-width": "^4.2.0", "which-module": "^2.0.0", "y18n": "^4.0.0", "yargs-parser": "^18.1.2"}, "deprecated": false, "description": "yargs the modern, pirate-themed, successor to optimist.", "devDependencies": {"@types/chai": "^4.2.11", "@types/decamelize": "^1.2.0", "@types/mocha": "^7.0.2", "@types/node": "^10.0.3", "@typescript-eslint/eslint-plugin": "^3.0.0", "@typescript-eslint/parser": "^3.0.0", "c8": "^7.0.0", "chai": "^4.2.0", "chalk": "^4.0.0", "coveralls": "^3.0.9", "cpr": "^3.0.1", "cross-spawn": "^7.0.0", "es6-promise": "^4.2.5", "eslint": "^6.8.0", "eslint-plugin-import": "^2.20.1", "eslint-plugin-node": "^11.0.0", "gts": "^2.0.0-alpha.4", "hashish": "0.0.4", "mocha": "^7.0.0", "rimraf": "^3.0.2", "standardx": "^5.0.0", "typescript": "^3.7.0", "which": "^2.0.0", "yargs-test-extends": "^1.0.1"}, "engines": {"node": ">=8"}, "files": ["index.js", "yargs.js", "build", "locales", "LICENSE"], "homepage": "https://yargs.js.org/", "keywords": ["argument", "args", "option", "parser", "parsing", "cli", "command"], "license": "MIT", "main": "./index.js", "name": "yargs", "repository": {"type": "git", "url": "git+https://github.com/yargs/yargs.git"}, "scripts": {"check": "standardx && standardx **/*.ts", "compile": "rimraf build && tsc", "coverage": "c8 report --check-coverage", "fix": "standardx --fix  && standardx --fix **/*.ts", "posttest": "npm run check", "prepare": "npm run compile", "pretest": "npm run compile -- -p tsconfig.test.json", "test": "c8 mocha --require ./test/before.js --timeout=12000 --check-leaks"}, "standardx": {"ignore": ["build", "**/example/**"]}, "version": "15.4.1"}