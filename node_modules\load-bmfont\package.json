{"_from": "load-bmfont@^1.4.1", "_id": "load-bmfont@1.4.2", "_inBundle": false, "_integrity": "sha512-qElWkmjW9Oq1F9EI5Gt7aD9zcdHb9spJCW1L/dmPf7KzCCEJxq8nhHz5eCgI9aMf7vrG/wyaCqdsI+Iy9ZTlog==", "_location": "/load-bmfont", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "load-bmfont@^1.4.1", "name": "load-bmfont", "escapedName": "load-bmfont", "rawSpec": "^1.4.1", "saveSpec": null, "fetchSpec": "^1.4.1"}, "_requiredBy": ["/@jimp/plugin-print"], "_resolved": "https://registry.npmmirror.com/load-bmfont/-/load-bmfont-1.4.2.tgz", "_shasum": "e0f4516064fa5be8439f9c3696c01423a64e8717", "_spec": "load-bmfont@^1.4.1", "_where": "D:\\codes\\Godspeed\\Date\\2025-07-20 尚明伟2\\node_modules\\@jimp\\plugin-print", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/mattdesl"}, "browser": "browser.js", "bugs": {"url": "https://github.com/Jam3/load-bmfont/issues"}, "bundleDependencies": false, "dependencies": {"buffer-equal": "0.0.1", "mime": "^1.3.4", "parse-bmfont-ascii": "^1.0.3", "parse-bmfont-binary": "^1.0.5", "parse-bmfont-xml": "^1.1.4", "phin": "^3.7.1", "xhr": "^2.0.1", "xtend": "^4.0.0"}, "deprecated": false, "description": "loads a BMFont file in Node and the browser", "devDependencies": {"browserify": "^9.0.3", "tap-spec": "^2.2.2", "tape": "^3.5.0", "testling": "^1.7.1"}, "homepage": "https://github.com/Jam3/load-bmfont", "keywords": ["bmfont", "bitmap", "font", "angel", "code", "angelcode", "parse", "ascii", "xml", "text", "json"], "license": "MIT", "main": "index.js", "name": "load-bmfont", "repository": {"type": "git", "url": "git://github.com/Jam3/load-bmfont.git"}, "scripts": {"test": "npm run test-node && npm run test-browser", "test-browser": "browserify test.js | testling | tap-spec", "test-node": "(node test.js; node test-server.js) | tap-spec"}, "version": "1.4.2"}