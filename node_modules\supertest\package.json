{"_from": "supertest@^6.3.3", "_id": "supertest@6.3.4", "_inBundle": false, "_integrity": "sha512-erY3HFDG0dPnhw4U+udPfrzXa4xhSG+n4rxfRuZWCUvjFWwKl+OxWf/7zk50s84/fAAs7vf5QAb9uRa0cCykxw==", "_location": "/supertest", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "supertest@^6.3.3", "name": "supertest", "escapedName": "supertest", "rawSpec": "^6.3.3", "saveSpec": null, "fetchSpec": "^6.3.3"}, "_requiredBy": ["#DEV:/"], "_resolved": "https://registry.npmmirror.com/supertest/-/supertest-6.3.4.tgz", "_shasum": "2145c250570c2ea5d337db3552dbfb78a2286218", "_spec": "supertest@^6.3.3", "_where": "D:\\codes\\Godspeed\\Date\\2025-07-20 尚明伟2", "author": {"name": "<PERSON><PERSON>"}, "bugs": {"url": "https://github.com/ladjs/supertest/issues"}, "bundleDependencies": false, "contributors": [], "dependencies": {"methods": "^1.1.2", "superagent": "^8.1.2"}, "deprecated": false, "description": "SuperAgent driven library for testing HTTP servers", "devDependencies": {"body-parser": "^1.20.2", "cookie-parser": "^1.4.6", "eslint": "^8.32.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-plugin-import": "^2.27.5", "express": "^4.18.2", "mocha": "^10.2.0", "nock": "^13.3.0", "nyc": "^15.1.0", "proxyquire": "^2.1.3", "should": "^13.2.3"}, "engines": {"node": ">=6.4.0"}, "files": ["index.js", "lib"], "homepage": "https://github.com/ladjs/supertest#readme", "keywords": ["bdd", "http", "request", "superagent", "tdd", "test", "testing"], "license": "MIT", "main": "index.js", "name": "supertest", "repository": {"type": "git", "url": "git+https://github.com/ladjs/supertest.git"}, "scripts": {"coverage": "nyc report --reporter=text-lcov > coverage.lcov", "lint": "eslint lib/**/*.js test/**/*.js index.js", "lint:fix": "eslint --fix lib/**/*.js test/**/*.js index.js", "pretest": "npm run lint --if-present", "test": "nyc --reporter=html --reporter=text mocha --exit --require should --reporter spec --check-leaks"}, "version": "6.3.4"}