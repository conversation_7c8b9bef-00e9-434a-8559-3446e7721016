{"version": 3, "file": "index.js", "names": ["PNG", "throwError", "isNodePattern", "MIME_TYPE", "PNG_FILTER_AUTO", "PNG_FILTER_NONE", "PNG_FILTER_SUB", "PNG_FILTER_UP", "PNG_FILTER_AVERAGE", "PNG_FILTER_PATH", "mime", "constants", "MIME_PNG", "has<PERSON><PERSON><PERSON>", "decoders", "sync", "read", "encoders", "data", "png", "width", "bitmap", "height", "write", "deflateLevel", "_deflateLevel", "deflateStrategy", "_deflateStrategy", "filterType", "_filterType", "colorType", "_colorType", "_rgba", "inputHasAlpha", "class", "l", "cb", "call", "Math", "round", "s", "f"], "sources": ["../src/index.js"], "sourcesContent": ["import { PNG } from \"pngjs\";\nimport { throwError, isNodePattern } from \"@jimp/utils\";\n\nconst MIME_TYPE = \"image/png\";\n\n// PNG filter types\nconst PNG_FILTER_AUTO = -1;\nconst PNG_FILTER_NONE = 0;\nconst PNG_FILTER_SUB = 1;\nconst PNG_FILTER_UP = 2;\nconst PNG_FILTER_AVERAGE = 3;\nconst PNG_FILTER_PATH = 4;\n\nexport default () => ({\n  mime: { [MIME_TYPE]: [\"png\"] },\n\n  constants: {\n    MIME_PNG: MIME_TYPE,\n    PNG_FILTER_AUTO,\n    PNG_FILTER_NONE,\n    PNG_FILTER_SUB,\n    PNG_FILTER_UP,\n    PNG_FILTER_AVERAGE,\n    PNG_FILTER_PATH,\n  },\n\n  hasAlpha: { [MIME_TYPE]: true },\n  decoders: { [MIME_TYPE]: PNG.sync.read },\n  encoders: {\n    [MIME_TYPE](data) {\n      const png = new PNG({\n        width: data.bitmap.width,\n        height: data.bitmap.height,\n      });\n\n      png.data = data.bitmap.data;\n\n      return PNG.sync.write(png, {\n        deflateLevel: data._deflateLevel,\n        deflateStrategy: data._deflateStrategy,\n        filterType: data._filterType,\n        colorType:\n          typeof data._colorType === \"number\"\n            ? data._colorType\n            : data._rgba\n            ? 6\n            : 2,\n        inputHasAlpha: data._rgba,\n      });\n    },\n  },\n\n  class: {\n    _deflateLevel: 9,\n    _deflateStrategy: 3,\n    _filterType: PNG_FILTER_AUTO,\n    _colorType: null,\n\n    /**\n     * Sets the deflate level used when saving as PNG format (default is 9)\n     * @param {number} l Deflate level to use 0-9. 0 is no compression. 9 (default) is maximum compression.\n     * @param {function(Error, Jimp)} cb (optional) a callback for when complete\n     * @returns {Jimp} this for chaining of methods\n     */\n    deflateLevel(l, cb) {\n      if (typeof l !== \"number\") {\n        return throwError.call(this, \"l must be a number\", cb);\n      }\n\n      if (l < 0 || l > 9) {\n        return throwError.call(this, \"l must be a number 0 - 9\", cb);\n      }\n\n      this._deflateLevel = Math.round(l);\n\n      if (isNodePattern(cb)) {\n        cb.call(this, null, this);\n      }\n\n      return this;\n    },\n\n    /**\n     * Sets the deflate strategy used when saving as PNG format (default is 3)\n     * @param {number} s Deflate strategy to use 0-3.\n     * @param {function(Error, Jimp)} cb (optional) a callback for when complete\n     * @returns {Jimp} this for chaining of methods\n     */\n    deflateStrategy(s, cb) {\n      if (typeof s !== \"number\") {\n        return throwError.call(this, \"s must be a number\", cb);\n      }\n\n      if (s < 0 || s > 3) {\n        return throwError.call(this, \"s must be a number 0 - 3\", cb);\n      }\n\n      this._deflateStrategy = Math.round(s);\n\n      if (isNodePattern(cb)) {\n        cb.call(this, null, this);\n      }\n\n      return this;\n    },\n\n    /**\n     * Sets the filter type used when saving as PNG format (default is automatic filters)\n     * @param {number} f The quality to use -1-4.\n     * @param {function(Error, Jimp)} cb (optional) a callback for when complete\n     * @returns {Jimp} this for chaining of methods\n     */\n    filterType(f, cb) {\n      if (typeof f !== \"number\") {\n        return throwError.call(this, \"n must be a number\", cb);\n      }\n\n      if (f < -1 || f > 4) {\n        return throwError.call(\n          this,\n          \"n must be -1 (auto) or a number 0 - 4\",\n          cb\n        );\n      }\n\n      this._filterType = Math.round(f);\n\n      if (isNodePattern(cb)) {\n        cb.call(this, null, this);\n      }\n\n      return this;\n    },\n    /**\n     * Sets the color type used when saving as PNG format\n     * @param {number} s color type to use 0, 2, 4, 6.\n     * @param {function(Error, Jimp)} cb (optional) a callback for when complete\n     * @returns {Jimp} this for chaining of methods\n     */ colorType(s, cb) {\n      if (typeof s !== \"number\") {\n        return throwError.call(this, \"s must be a number\", cb);\n      }\n\n      if (s !== 0 && s !== 2 && s !== 4 && s !== 6) {\n        return throwError.call(this, \"s must be a number 0, 2, 4, 6.\", cb);\n      }\n\n      this._colorType = Math.round(s);\n\n      if (isNodePattern(cb)) {\n        cb.call(this, null, this);\n      }\n\n      return this;\n    },\n  },\n});\n"], "mappings": "AAAA,SAASA,GAAG,QAAQ,OAAO;AAC3B,SAASC,UAAU,EAAEC,aAAa,QAAQ,aAAa;AAEvD,MAAMC,SAAS,GAAG,WAAW;;AAE7B;AACA,MAAMC,eAAe,GAAG,CAAC,CAAC;AAC1B,MAAMC,eAAe,GAAG,CAAC;AACzB,MAAMC,cAAc,GAAG,CAAC;AACxB,MAAMC,aAAa,GAAG,CAAC;AACvB,MAAMC,kBAAkB,GAAG,CAAC;AAC5B,MAAMC,eAAe,GAAG,CAAC;AAEzB,gBAAe,OAAO;EACpBC,IAAI,EAAE;IAAE,CAACP,SAAS,GAAG,CAAC,KAAK;EAAE,CAAC;EAE9BQ,SAAS,EAAE;IACTC,QAAQ,EAAET,SAAS;IACnBC,eAAe;IACfC,eAAe;IACfC,cAAc;IACdC,aAAa;IACbC,kBAAkB;IAClBC;EACF,CAAC;EAEDI,QAAQ,EAAE;IAAE,CAACV,SAAS,GAAG;EAAK,CAAC;EAC/BW,QAAQ,EAAE;IAAE,CAACX,SAAS,GAAGH,GAAG,CAACe,IAAI,CAACC;EAAK,CAAC;EACxCC,QAAQ,EAAE;IACR,CAACd,SAAS,EAAEe,IAAI,EAAE;MAChB,MAAMC,GAAG,GAAG,IAAInB,GAAG,CAAC;QAClBoB,KAAK,EAAEF,IAAI,CAACG,MAAM,CAACD,KAAK;QACxBE,MAAM,EAAEJ,IAAI,CAACG,MAAM,CAACC;MACtB,CAAC,CAAC;MAEFH,GAAG,CAACD,IAAI,GAAGA,IAAI,CAACG,MAAM,CAACH,IAAI;MAE3B,OAAOlB,GAAG,CAACe,IAAI,CAACQ,KAAK,CAACJ,GAAG,EAAE;QACzBK,YAAY,EAAEN,IAAI,CAACO,aAAa;QAChCC,eAAe,EAAER,IAAI,CAACS,gBAAgB;QACtCC,UAAU,EAAEV,IAAI,CAACW,WAAW;QAC5BC,SAAS,EACP,OAAOZ,IAAI,CAACa,UAAU,KAAK,QAAQ,GAC/Bb,IAAI,CAACa,UAAU,GACfb,IAAI,CAACc,KAAK,GACV,CAAC,GACD,CAAC;QACPC,aAAa,EAAEf,IAAI,CAACc;MACtB,CAAC,CAAC;IACJ;EACF,CAAC;EAEDE,KAAK,EAAE;IACLT,aAAa,EAAE,CAAC;IAChBE,gBAAgB,EAAE,CAAC;IACnBE,WAAW,EAAEzB,eAAe;IAC5B2B,UAAU,EAAE,IAAI;IAEhB;AACJ;AACA;AACA;AACA;AACA;IACIP,YAAY,CAACW,CAAC,EAAEC,EAAE,EAAE;MAClB,IAAI,OAAOD,CAAC,KAAK,QAAQ,EAAE;QACzB,OAAOlC,UAAU,CAACoC,IAAI,CAAC,IAAI,EAAE,oBAAoB,EAAED,EAAE,CAAC;MACxD;MAEA,IAAID,CAAC,GAAG,CAAC,IAAIA,CAAC,GAAG,CAAC,EAAE;QAClB,OAAOlC,UAAU,CAACoC,IAAI,CAAC,IAAI,EAAE,0BAA0B,EAAED,EAAE,CAAC;MAC9D;MAEA,IAAI,CAACX,aAAa,GAAGa,IAAI,CAACC,KAAK,CAACJ,CAAC,CAAC;MAElC,IAAIjC,aAAa,CAACkC,EAAE,CAAC,EAAE;QACrBA,EAAE,CAACC,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;MAC3B;MAEA,OAAO,IAAI;IACb,CAAC;IAED;AACJ;AACA;AACA;AACA;AACA;IACIX,eAAe,CAACc,CAAC,EAAEJ,EAAE,EAAE;MACrB,IAAI,OAAOI,CAAC,KAAK,QAAQ,EAAE;QACzB,OAAOvC,UAAU,CAACoC,IAAI,CAAC,IAAI,EAAE,oBAAoB,EAAED,EAAE,CAAC;MACxD;MAEA,IAAII,CAAC,GAAG,CAAC,IAAIA,CAAC,GAAG,CAAC,EAAE;QAClB,OAAOvC,UAAU,CAACoC,IAAI,CAAC,IAAI,EAAE,0BAA0B,EAAED,EAAE,CAAC;MAC9D;MAEA,IAAI,CAACT,gBAAgB,GAAGW,IAAI,CAACC,KAAK,CAACC,CAAC,CAAC;MAErC,IAAItC,aAAa,CAACkC,EAAE,CAAC,EAAE;QACrBA,EAAE,CAACC,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;MAC3B;MAEA,OAAO,IAAI;IACb,CAAC;IAED;AACJ;AACA;AACA;AACA;AACA;IACIT,UAAU,CAACa,CAAC,EAAEL,EAAE,EAAE;MAChB,IAAI,OAAOK,CAAC,KAAK,QAAQ,EAAE;QACzB,OAAOxC,UAAU,CAACoC,IAAI,CAAC,IAAI,EAAE,oBAAoB,EAAED,EAAE,CAAC;MACxD;MAEA,IAAIK,CAAC,GAAG,CAAC,CAAC,IAAIA,CAAC,GAAG,CAAC,EAAE;QACnB,OAAOxC,UAAU,CAACoC,IAAI,CACpB,IAAI,EACJ,uCAAuC,EACvCD,EAAE,CACH;MACH;MAEA,IAAI,CAACP,WAAW,GAAGS,IAAI,CAACC,KAAK,CAACE,CAAC,CAAC;MAEhC,IAAIvC,aAAa,CAACkC,EAAE,CAAC,EAAE;QACrBA,EAAE,CAACC,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;MAC3B;MAEA,OAAO,IAAI;IACb,CAAC;IACD;AACJ;AACA;AACA;AACA;AACA;IAAQP,SAAS,CAACU,CAAC,EAAEJ,EAAE,EAAE;MACnB,IAAI,OAAOI,CAAC,KAAK,QAAQ,EAAE;QACzB,OAAOvC,UAAU,CAACoC,IAAI,CAAC,IAAI,EAAE,oBAAoB,EAAED,EAAE,CAAC;MACxD;MAEA,IAAII,CAAC,KAAK,CAAC,IAAIA,CAAC,KAAK,CAAC,IAAIA,CAAC,KAAK,CAAC,IAAIA,CAAC,KAAK,CAAC,EAAE;QAC5C,OAAOvC,UAAU,CAACoC,IAAI,CAAC,IAAI,EAAE,gCAAgC,EAAED,EAAE,CAAC;MACpE;MAEA,IAAI,CAACL,UAAU,GAAGO,IAAI,CAACC,KAAK,CAACC,CAAC,CAAC;MAE/B,IAAItC,aAAa,CAACkC,EAAE,CAAC,EAAE;QACrBA,EAAE,CAACC,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;MAC3B;MAEA,OAAO,IAAI;IACb;EACF;AACF,CAAC,CAAC"}