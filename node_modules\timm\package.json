{"name": "timm", "version": "1.7.1", "description": "Immutability helpers with fast reads and acceptable writes", "author": "<PERSON>", "license": "MIT", "keywords": ["immutability"], "homepage": "http://guigrpa.github.io/timm/", "repository": {"type": "git", "url": "https://github.com/guigrpa/timm.git"}, "main": "lib/timm.js", "types": "lib/timm.d.ts", "scripts": {"typecheck": "tsc --pretty", "lint": "eslint src/timm.ts", "jest": "yarn test --watch", "test": "jest --coverage src", "compile": "yarn compile:clean && yarn compile:main && yarn compile:types", "compile:clean": "rm -rf ./lib", "compile:main": "babel src -d lib --extensions '.ts'", "compile:types": "yarn typecheck", "docs": "extract-docs --template docs/README_TEMPLATE.md --output README.md", "build": "yarn lint && yarn compile && yarn test && yarn docs && yarn xxl", "xxl": "xxl", "benchmarks": "node tools/benchmarks", "prettier": "prettier --single-quote --trailing-comma es5 --write \"src/**/*.[jt]s\""}, "dependencies": {}, "devDependencies": {"@babel/cli": "^7.11.6", "@babel/core": "^7.11.6", "@babel/preset-env": "^7.11.5", "@babel/preset-typescript": "^7.10.1", "@typescript-eslint/eslint-plugin": "^4.1.1", "@typescript-eslint/parser": "^4.1.1", "eslint": "^7.9.0", "eslint-config-prettier": "^6.11.0", "oao": "^2.0.0", "typescript": "^4.0.2", "babel-jest": "^26.3.0", "chalk": "1.1.3", "extract-docs": "^1.6.1", "immutability-helper": "^2.8.1", "immutable": "3.8.2", "jest": "^26.4.2", "lodash": "^4.17.20", "prettier": "^2.1.2", "seamless-immutable": "^7.1.4", "xxl": "^1.3.0"}, "_resolved": "https://registry.npmmirror.com/timm/-/timm-1.7.1.tgz", "_integrity": "sha512-IjZc9KIotudix8bMaBW6QvMuq64BrJWFs1+4V0lXwWGQZwH+LnX87doAYhem4caOEusRP9/g6jVDQmZ8XOk1nw==", "_from": "timm@^1.6.1"}