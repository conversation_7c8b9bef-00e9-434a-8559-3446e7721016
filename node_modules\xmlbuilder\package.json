{"_from": "xmlbuilder@~11.0.0", "_id": "xmlbuilder@11.0.1", "_inBundle": false, "_integrity": "sha512-fDlsI/kFEx7gLvbecc0/ohLG50fugQp8ryHzMTuW9vSa1GJ0XYWKnhsUx7oie3G98+r56aTQIUB4kht42R3JvA==", "_location": "/xmlbuilder", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "xmlbuilder@~11.0.0", "name": "xmlbuilder", "escapedName": "xmlbuilder", "rawSpec": "~11.0.0", "saveSpec": null, "fetchSpec": "~11.0.0"}, "_requiredBy": ["/xml2js"], "_resolved": "https://registry.npmmirror.com/xmlbuilder/-/xmlbuilder-11.0.1.tgz", "_shasum": "be9bae1c8a046e76b31127726347d0ad7002beb3", "_spec": "xmlbuilder@~11.0.0", "_where": "D:\\codes\\Godspeed\\Date\\2025-07-20 尚明伟2\\node_modules\\xml2js", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "http://github.com/oozcitak/xmlbuilder-js/issues"}, "bundleDependencies": false, "contributors": [], "dependencies": {}, "deprecated": false, "description": "An XML builder for node.js", "devDependencies": {"coffee-coverage": "2.*", "coffeescript": "1.*", "coveralls": "*", "istanbul": "*", "mocha": "*", "xpath": "*"}, "engines": {"node": ">=4.0"}, "homepage": "http://github.com/oozcitak/xmlbuilder-js", "keywords": ["xml", "xmlbuilder"], "license": "MIT", "main": "./lib/index", "name": "xmlbuilder", "repository": {"type": "git", "url": "git://github.com/oozcitak/xmlbuilder-js.git"}, "scripts": {"postpublish": "rm -rf lib", "prepublishOnly": "coffee -co lib src", "test": "mocha \"test/**/*.coffee\" && istanbul report text lcov"}, "typings": "./typings/index.d.ts", "version": "11.0.1"}