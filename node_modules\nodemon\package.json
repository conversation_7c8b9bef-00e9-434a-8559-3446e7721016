{"_from": "nodemon@^2.0.22", "_id": "nodemon@2.0.22", "_inBundle": false, "_integrity": "sha512-B8YqaKMmyuCO7BowF1Z1/mkPqLk6cs/l63Ojtd6otKjMx47Dq1utxfRxcavH1I7VSaL8n5BUaoutadnsX3AAVQ==", "_location": "/nodemon", "_phantomChildren": {"ms": "2.1.3"}, "_requested": {"type": "range", "registry": true, "raw": "nodemon@^2.0.22", "name": "nodemon", "escapedName": "nodemon", "rawSpec": "^2.0.22", "saveSpec": null, "fetchSpec": "^2.0.22"}, "_requiredBy": ["#DEV:/"], "_resolved": "https://registry.npmmirror.com/nodemon/-/nodemon-2.0.22.tgz", "_shasum": "182c45c3a78da486f673d6c1702e00728daf5258", "_spec": "nodemon@^2.0.22", "_where": "D:\\codes\\Godspeed\\Date\\2025-07-20 尚明伟2", "author": {"name": "<PERSON><PERSON>", "url": "https://github.com/remy"}, "bin": {"nodemon": "bin/nodemon.js"}, "bugs": {"url": "https://github.com/remy/nodemon/issues"}, "bundleDependencies": false, "dependencies": {"chokidar": "^3.5.2", "debug": "^3.2.7", "ignore-by-default": "^1.0.1", "minimatch": "^3.1.2", "pstree.remy": "^1.1.8", "semver": "^5.7.1", "simple-update-notifier": "^1.0.7", "supports-color": "^5.5.0", "touch": "^3.1.0", "undefsafe": "^2.0.5"}, "deprecated": false, "description": "Simple monitor script for use during development of a Node.js app.", "devDependencies": {"@commitlint/cli": "^11.0.0", "@commitlint/config-conventional": "^11.0.0", "async": "1.4.2", "coffee-script": "~1.7.1", "eslint": "^7.32.0", "husky": "^7.0.4", "mocha": "^2.5.3", "nyc": "^15.1.0", "proxyquire": "^1.8.0", "semantic-release": "^18.0.0", "should": "~4.0.0"}, "engines": {"node": ">=8.10.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/nodemon"}, "homepage": "https://nodemon.io", "keywords": ["cli", "monitor", "monitor", "development", "restart", "autoload", "reload", "terminal"], "license": "MIT", "main": "./lib/nodemon", "name": "nodemon", "repository": {"type": "git", "url": "git+https://github.com/remy/nodemon.git"}, "scripts": {"clean": "rm -rf test/fixtures/test*.js test/fixtures/test*.md", "commitmsg": "commitlint -e", "coverage": "istanbul cover _mocha -- --timeout 30000 --ui bdd --reporter list test/**/*.test.js", "killall": "ps auxww | grep node | grep -v grep | awk '{ print $2 }' | xargs kill -9", "lint": "eslint lib/**/*.js", "postspec": "npm run clean", "prepush": "npm run lint", "semantic-release": "semantic-release", "spec": "for FILE in test/**/*.test.js; do echo $FILE; TEST=1 mocha --exit --timeout 30000 $FILE; if [ $? -ne 0 ]; then exit 1; fi; sleep 1; done", "test": "npm run lint && npm run spec", "web": "node web"}, "version": "2.0.22"}