{"_from": "xml2js@^0.5.0", "_id": "xml2js@0.5.0", "_inBundle": false, "_integrity": "sha512-drPFnkQJik/O+uPKpqSgr22mpuFHqKdbS835iAQrUC73L2F5WkboIRd63ai/2Yg6I1jzifPFKH2NTK+cfglkIA==", "_location": "/xml2js", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "xml2js@^0.5.0", "name": "xml2js", "escapedName": "xml2js", "rawSpec": "^0.5.0", "saveSpec": null, "fetchSpec": "^0.5.0"}, "_requiredBy": ["/parse-bmfont-xml"], "_resolved": "https://registry.npmmirror.com/xml2js/-/xml2js-0.5.0.tgz", "_shasum": "d9440631fbb2ed800203fad106f2724f62c493b7", "_spec": "xml2js@^0.5.0", "_where": "D:\\codes\\Godspeed\\Date\\2025-07-20 尚明伟2\\node_modules\\parse-bmfont-xml", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://xivilization.net"}, "bugs": {"url": "https://github.com/<PERSON><PERSON>-from-XIV/node-xml2js/issues"}, "bundleDependencies": false, "contributors": [{"name": "maqr", "email": "<EMAIL>", "url": "https://github.com/maqr"}, {"name": "<PERSON>", "url": "http://benweaver.com/"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/jaekwon"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>", "url": "http://www.saltwaterc.eu/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://cartercole.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://www.kurtraschke.com/"}, {"name": "Contra", "email": "<EMAIL>", "url": "https://github.com/Contra"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/mdiniz"}, {"name": "<PERSON>", "url": "https://github.com/mhart"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://zacharyscott.net/"}, {"name": "<PERSON>", "url": "https://github.com/raoulmillais"}, {"name": "Salsita Software", "url": "http://www.salsitasoft.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://www.emotive.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://weibo.com/shyvo"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/Sitin"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/christav"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://f2e.us/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://www.bitstorm.it/"}, {"name": "<PERSON>", "url": "http://jacksenechal.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/hoelzl"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://www.creynders.be/"}, {"name": "<PERSON>", "url": "https://github.com/tsgautier"}, {"name": "<PERSON>", "url": "https://github.com/toddrbryan"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://leoreavidar.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://www.actionshrimp.com/"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Candle", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jes.st"}, {"name": "<PERSON>", "email": "<<EMAIL>", "url": "http://compton.nu/"}, {"name": "<PERSON><PERSON><PERSON>", "url": "http://rocha.la/"}, {"name": "<PERSON>", "url": "https://github.com/micha<PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/ryedin"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/elaberge"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://twitter.com/benjamincoe"}, {"name": "<PERSON>", "url": "https://github.com/cressie176"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://www.hacksrus.net/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://fiznool.com/"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/tflanagan"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/Tim<PERSON>ns"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/TrySound"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://codesleuth.co.uk/"}, {"name": "<PERSON>", "url": "https://github.com/nmaquet"}, {"name": "<PERSON><PERSON>", "url": "http://lovell.info/"}, {"name": "d3adc0d3", "url": "https://github.com/d3adc0d3"}, {"name": "<PERSON>", "url": "https://github.com/autopulated"}], "dependencies": {"sax": ">=0.6.0", "xmlbuilder": "~11.0.0"}, "deprecated": false, "description": "Simple XML to JavaScript object converter.", "devDependencies": {"coffee-script": ">=1.10.0", "coveralls": "^3.0.1", "diff": ">=1.0.8", "docco": ">=0.6.2", "nyc": ">=2.2.1", "zap": ">=0.2.9"}, "directories": {"lib": "./lib"}, "engines": {"node": ">=4.0.0"}, "files": ["lib"], "homepage": "https://github.com/<PERSON><PERSON>-from-XIV/node-xml2js", "keywords": ["xml", "json"], "license": "MIT", "main": "./lib/xml2js", "name": "xml2js", "repository": {"type": "git", "url": "git+https://github.com/Leon<PERSON>-from-XIV/node-xml2js.git"}, "scripts": {"build": "cake build", "coverage": "nyc npm test && nyc report", "coveralls": "nyc npm test && nyc report --reporter=text-lcov | coveralls", "doc": "cake doc", "test": "zap"}, "version": "0.5.0"}