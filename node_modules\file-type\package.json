{"name": "file-type", "version": "16.5.4", "description": "Detect the file type of a Buffer/Uint8Array/ArrayBuffer", "license": "MIT", "repository": "sindresorhus/file-type", "funding": "https://github.com/sindresorhus/file-type?sponsor=1", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "engines": {"node": ">=10"}, "scripts": {"ava": "ava --serial --verbose", "test": "xo && ava && tsd"}, "files": ["index.js", "index.d.ts", "browser.js", "browser.d.ts", "core.js", "core.d.ts", "supported.js", "util.js"], "keywords": ["mime", "file", "type", "magic", "archive", "image", "img", "pic", "picture", "flash", "photo", "video", "detect", "check", "is", "exif", "exe", "binary", "buffer", "uint8array", "jpg", "png", "apng", "gif", "webp", "flif", "xcf", "cr2", "cr3", "orf", "arw", "dng", "nef", "rw2", "raf", "tif", "bmp", "icns", "jxr", "psd", "indd", "zip", "tar", "rar", "gz", "bz2", "7z", "dmg", "mp4", "mid", "mkv", "webm", "mov", "avi", "mpg", "mp2", "mp3", "m4a", "ogg", "opus", "flac", "wav", "amr", "pdf", "epub", "mobi", "swf", "rtf", "woff", "woff2", "eot", "ttf", "otf", "ico", "flv", "ps", "xz", "sqlite", "xpi", "cab", "deb", "ar", "rpm", "Z", "lz", "cfb", "mxf", "mts", "wasm", "webassembly", "blend", "bpg", "docx", "pptx", "xlsx", "3gp", "jp2", "jpm", "jpx", "mj2", "aif", "odt", "ods", "odp", "xml", "heic", "ics", "glb", "pcap", "dsf", "lnk", "alias", "voc", "ac3", "3g2", "m4b", "m4p", "m4v", "f4a", "f4b", "f4p", "f4v", "mie", "qcp", "asf", "ogv", "ogm", "oga", "spx", "ogx", "ape", "wv", "cur", "nes", "crx", "ktx", "dcm", "mpc", "arrow", "shp", "aac", "mp1", "it", "s3m", "xm", "ai", "skp", "avif", "eps", "lzh", "pgp", "asar", "stl", "chm", "3mf", "zst", "jxl", "vcf"], "devDependencies": {"@types/node": "^13.1.4", "ava": "^2.3.0", "noop-stream": "^0.1.0", "read-chunk": "^3.2.0", "tsd": "^0.11.0", "xo": "^0.25.3"}, "dependencies": {"readable-web-to-node-stream": "^3.0.0", "strtok3": "^6.2.4", "token-types": "^4.1.1"}, "xo": {"envs": ["node", "browser"], "rules": {"no-inner-declarations": "warn", "no-await-in-loop": "warn", "promise/prefer-await-to-then": "warn", "prefer-named-capture-group": "off"}}, "_resolved": "https://registry.npmmirror.com/file-type/-/file-type-16.5.4.tgz", "_integrity": "sha512-/yFHK0aGjFEgDJjEKP0pWCplsPFPhwyfwevf/pVxiN0tmE4L9LmwWxWukdJSHdoCli4VgQLehjJtwQBnqmsKcw==", "_from": "file-type@^16.5.4"}