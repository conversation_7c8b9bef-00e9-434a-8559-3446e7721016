{"version": 3, "file": "neuquantFloat.d.ts", "sourceRoot": "", "sources": ["../../../../../src/palette/neuquant/neuquantFloat.ts"], "names": [], "mappings": "AA6BA,OAAO,EAAE,cAAc,EAAE,MAAM,4BAA4B,CAAC;AAC5D,OAAO,EAAE,0BAA0B,EAAE,MAAM,mCAAmC,CAAC;AAC/E,OAAO,EAAE,wBAAwB,EAAE,MAAM,qBAAqB,CAAC;AAC/D,OAAO,EAAE,0BAA0B,EAAE,MAAM,+BAA+B,CAAC;AAsC3E,qBAAa,aAAc,SAAQ,wBAAwB;IAKzD,OAAO,CAAC,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAO;IACtC,OAAO,CAAC,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAO;IACtC,OAAO,CAAC,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAO;IACtC,OAAO,CAAC,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAO;IACtC,OAAO,CAAC,MAAM,CAAC,QAAQ,CAAC,gBAAgB,CAAyB;IAGjE,OAAO,CAAC,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAO;IAGvC,OAAO,CAAC,MAAM,CAAC,QAAQ,CAAC,iBAAiB,CAAM;IAG/C,OAAO,CAAC,MAAM,CAAC,QAAQ,CAAC,YAAY,CAAwC;IAC5E,OAAO,CAAC,MAAM,CAAC,QAAQ,CAAC,WAAW,CAAM;IAKzC,OAAO,CAAC,MAAM,CAAC,QAAQ,CAAC,UAAU,CAAM;IACxC,OAAO,CAAC,MAAM,CAAC,QAAQ,CAAC,KAAK,CAC4B;IAGzD,OAAO,CAAC,MAAM,CAAC,QAAQ,CAAC,UAAU,CAEuB;IAKzD,OAAO,CAAC,MAAM,CAAC,QAAQ,CAAC,gBAAgB,CAAK;IAG7C,OAAO,CAAC,MAAM,CAAC,QAAQ,CAAC,WAAW,CAAuC;IAG1E,OAAO,CAAC,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAM;IAK7C,OAAO,CAAC,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAM;IAG7C,OAAO,CAAC,MAAM,CAAC,QAAQ,CAAC,UAAU,CAAsC;IAGxE,OAAO,CAAC,MAAM,CAAC,QAAQ,CAAC,aAAa,CAAK;IAC1C,OAAO,CAAC,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAoC;IACpE,OAAO,CAAC,MAAM,CAAC,QAAQ,CAAC,kBAAkB,CACoB;IAC9D,OAAO,CAAC,MAAM,CAAC,QAAQ,CAAC,aAAa,CAAyC;IAE9E,OAAO,CAAC,WAAW,CAAW;IAC9B,OAAO,CAAC,QAAQ,CAAC,YAAY,CAAU;IACvC,OAAO,CAAC,QAAQ,CAAiB;IAEjC,4BAA4B;IAC5B,OAAO,CAAC,QAAQ,CAAC,aAAa,CAAU;IACxC,OAAO,CAAC,SAAS,CAAY;IAG7B,OAAO,CAAC,KAAK,CAAY;IAGzB,OAAO,CAAC,KAAK,CAAY;IACzB,OAAO,CAAC,QAAQ,CAAC,SAAS,CAA6B;gBAGrD,uBAAuB,EAAE,0BAA0B,EACnD,MAAM,SAAM;IAgBd,MAAM,CAAC,cAAc,EAAE,cAAc;IAIpC,QAAQ;IAUT,OAAO,CAAC,KAAK;IAgBb;;OAEG;IACH,OAAO,CAAE,MAAM;IA0Ef,OAAO,CAAC,aAAa;IAWrB;;OAEG;IACH,OAAO,CAAC,eAAe;IAgCvB;;OAEG;IACH,OAAO,CAAC,YAAY;IAoBpB;;;;;;;;;;OAUG;IACH,OAAO,CAAC,QAAQ;CAiCjB"}