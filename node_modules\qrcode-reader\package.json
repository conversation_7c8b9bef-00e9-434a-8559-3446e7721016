{"_from": "qrcode-reader@^1.0.4", "_id": "qrcode-reader@1.0.4", "_inBundle": false, "_integrity": "sha512-rRjALGNh9zVqvweg1j5OKIQKNsw3bLC+7qwlnead5K/9cb1cEIAGkwikt/09U0K+2IDWGD9CC6SP7tHAjUeqvQ==", "_location": "/qrcode-reader", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "qrcode-reader@^1.0.4", "name": "qrcode-reader", "escapedName": "qrcode-reader", "rawSpec": "^1.0.4", "saveSpec": null, "fetchSpec": "^1.0.4"}, "_requiredBy": ["#USER", "/"], "_resolved": "https://registry.npmmirror.com/qrcode-reader/-/qrcode-reader-1.0.4.tgz", "_shasum": "95d9bb9e8130800361a96cb5a43124ad1d9e06b8", "_spec": "qrcode-reader@^1.0.4", "_where": "D:\\codes\\Godspeed\\Date\\2025-07-20 尚明伟2", "author": "", "bugs": {"url": "https://github.com/edi9999/jsqrcode/issues"}, "bundleDependencies": false, "deprecated": false, "description": "fork of lazarsoft's jsqrcode for node", "devDependencies": {"chai": "^4.1.2", "eslint": "^4.7.2", "image-parser": "^1.2.5", "jimp": "^0.2.28", "mocha": "^3.5.3", "rollup": "^0.50.0", "uglify-js": "^3.1.2"}, "homepage": "https://github.com/edi9999/jsqrcode", "jsnext:main": "src/index.js", "license": "Apache-2.0", "main": "dist/index.js", "module": "src/index.js", "name": "qrcode-reader", "repository": {"type": "git", "url": "git+https://github.com/edi9999/jsqrcode.git"}, "scripts": {"build": "rollup -c", "build-and-test": "npm run build && npm test", "lint": "eslint src test", "minify": "uglifyjs dist/index.js -o dist/index.min.js --compress --mangle", "pretest": "npm run lint", "preversion": "npm run lint && npm run build && npm run minify && npm test", "test": "mocha", "watch": "rollup -c -w"}, "version": "1.0.4"}