{"_from": "yargs-parser@^18.1.2", "_id": "yargs-parser@18.1.3", "_inBundle": false, "_integrity": "sha512-o50j0JeToy/4K6OZcaQmW6lyXXKhq7csREXcDwk2omFPJEwUNOVtJKvmDr9EI1fAJZUyZcRF7kxGBWmRXudrCQ==", "_location": "/yargs-parser", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "yargs-parser@^18.1.2", "name": "yargs-parser", "escapedName": "yargs-parser", "rawSpec": "^18.1.2", "saveSpec": null, "fetchSpec": "^18.1.2"}, "_requiredBy": ["/yargs"], "_resolved": "https://registry.npmmirror.com/yargs-parser/-/yargs-parser-18.1.3.tgz", "_shasum": "be68c4975c6b2abf469236b0c870362fab09a7b0", "_spec": "yargs-parser@^18.1.2", "_where": "D:\\codes\\Godspeed\\Date\\2025-07-20 尚明伟2\\node_modules\\yargs", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/yargs/yargs-parser/issues"}, "bundleDependencies": false, "dependencies": {"camelcase": "^5.0.0", "decamelize": "^1.2.0"}, "deprecated": false, "description": "the mighty option parser used by yargs", "devDependencies": {"c8": "^7.0.1", "chai": "^4.2.0", "mocha": "^7.0.0", "standard": "^14.3.1"}, "engines": {"node": ">=6"}, "files": ["lib", "index.js"], "homepage": "https://github.com/yargs/yargs-parser#readme", "keywords": ["argument", "parser", "yargs", "command", "cli", "parsing", "option", "args", "argument"], "license": "ISC", "main": "index.js", "name": "yargs-parser", "repository": {"type": "git", "url": "git+https://github.com/yargs/yargs-parser.git"}, "scripts": {"coverage": "c8 report --check-coverage check-coverage --lines=100 --branches=97 --statements=100", "fix": "standard --fix", "posttest": "standard", "test": "c8 --reporter=text --reporter=html  mocha test/*.js"}, "version": "18.1.3"}