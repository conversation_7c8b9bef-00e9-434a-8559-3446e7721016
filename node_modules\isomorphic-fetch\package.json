{"name": "isomorphic-fetch", "version": "3.0.0", "description": "Isomorphic WHATWG Fetch API, for Node & Browserify", "browser": "fetch-npm-browserify.js", "main": "fetch-npm-node.js", "scripts": {"files": "find . -name '*.js' ! -path './node_modules/*' ! -path './bower_components/*'", "test": "jshint `npm run -s files` && lintspaces -i js-comments -e .editorconfig `npm run -s files` && mocha"}, "repository": {"type": "git", "url": "https://github.com/matthew-andrews/isomorphic-fetch.git"}, "author": "<PERSON> <<EMAIL>>", "license": "MIT", "bugs": {"url": "https://github.com/matthew-andrews/isomorphic-fetch/issues"}, "homepage": "https://github.com/matthew-andrews/isomorphic-fetch/issues", "dependencies": {"node-fetch": "^2.6.1", "whatwg-fetch": "^3.4.1"}, "devDependencies": {"chai": "^4.2.0", "jshint": "^2.5.11", "lintspaces-cli": "^0.7.1", "mocha": "^8.1.3", "nock": "^13.0.4"}, "_resolved": "https://registry.npmmirror.com/isomorphic-fetch/-/isomorphic-fetch-3.0.0.tgz", "_integrity": "sha512-qvUtwJ3j6qwsF3jLxkZ72qCgjMysPzDfeV240JHiGZsANBYd+EEuu35v7dfrJ9Up0Ak07D7GGSkGhCHTqg/5wA==", "_from": "isomorphic-fetch@^3.0.0"}