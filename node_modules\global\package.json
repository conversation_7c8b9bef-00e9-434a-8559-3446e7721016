{"name": "global", "version": "4.4.0", "description": "Require global variables", "keywords": [], "author": "Raynos <<EMAIL>>", "repository": "git://github.com/Raynos/global.git", "main": "window.js", "homepage": "https://github.com/Raynos/global", "contributors": [{"name": "<PERSON><PERSON>"}], "bugs": {"url": "https://github.com/Raynos/global/issues", "email": "<EMAIL>"}, "browser": {"min-document": false, "individual": false}, "dependencies": {"min-document": "^2.19.0", "process": "^0.11.10"}, "devDependencies": {"tape": "^2.12.0"}, "license": "MIT", "scripts": {"test": "node ./test", "build": "browserify test/index.js -o test/static/bundle.js", "testem": "testem"}, "testling": {"files": "test/index.js", "browsers": {"ie": ["8", "9", "10"], "firefox": ["16", "17", "nightly"], "chrome": ["22", "23", "canary"], "opera": ["12", "next"], "safari": ["5.1"]}}, "_resolved": "https://registry.npmmirror.com/global/-/global-4.4.0.tgz", "_integrity": "sha512-wv/LAoHdRE3BeTGz53FAamhGlPLhlssK45usmGFThIi4XqnBmjKQ16u+RNbP7WvigRZDxUsM0J3gcQ5yicaL0w==", "_from": "global@~4.4.0"}