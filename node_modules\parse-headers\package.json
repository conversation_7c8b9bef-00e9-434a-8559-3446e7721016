{"name": "parse-headers", "version": "2.0.6", "description": "Parse http headers, works with browserify/xhr", "main": "parse-headers.js", "types": "index.d.ts", "scripts": {"test": "node test.js"}, "repository": {"type": "git", "url": "https://github.com/kesla/parse-headers.git"}, "keywords": ["http", "headers"], "author": "<PERSON> <<EMAIL>>", "license": "MIT", "bugs": {"url": "https://github.com/kesla/parse-headers/issues"}, "homepage": "https://github.com/kesla/parse-headers", "devDependencies": {"tape": "^4.10.1"}, "dependencies": {}, "_resolved": "https://registry.npmmirror.com/parse-headers/-/parse-headers-2.0.6.tgz", "_integrity": "sha512-Tz11t3uKztEW5FEVZnj1ox8GKblWn+PvHY9TmJV5Mll2uHEwRdR/5Li1OlXoECjLYkApdhWy44ocONwXLiKO5A==", "_from": "parse-headers@^2.0.0"}