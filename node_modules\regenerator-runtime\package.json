{"name": "regenerator-runtime", "author": "<PERSON> <<EMAIL>>", "description": "Runtime for Regenerator-compiled generator and async functions.", "version": "0.13.11", "main": "runtime.js", "keywords": ["regenerator", "runtime", "generator", "async"], "sideEffects": true, "repository": {"type": "git", "url": "https://github.com/facebook/regenerator/tree/main/packages/runtime"}, "license": "MIT", "_resolved": "https://registry.npmmirror.com/regenerator-runtime/-/regenerator-runtime-0.13.11.tgz", "_integrity": "sha512-kY1AZVr2Ra+t+piVaJ4gxaFaReZVH40AKNo7UCX6W+dEwBo/2oZJzqfuN1qLq1oL45o56cPaTXELwrTh8Fpggg==", "_from": "regenerator-runtime@^0.13.3"}