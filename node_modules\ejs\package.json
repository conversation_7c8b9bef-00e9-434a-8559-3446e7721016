{"_from": "ejs@^3.1.8", "_id": "ejs@3.1.10", "_inBundle": false, "_integrity": "sha512-UeJmFfOrAQS8OJWPZ4qtgHyWExa088/MtK5UEyoJGFH67cDEXkZSviOiKRCZ4Xij0zxI3JECgYs3oKx+AizQBA==", "_location": "/ejs", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "ejs@^3.1.8", "name": "ejs", "escapedName": "ejs", "rawSpec": "^3.1.8", "saveSpec": null, "fetchSpec": "^3.1.8"}, "_requiredBy": ["#USER", "/"], "_resolved": "https://registry.npmmirror.com/ejs/-/ejs-3.1.10.tgz", "_shasum": "69ab8358b14e896f80cc39e62087b88500c3ac3b", "_spec": "ejs@^3.1.8", "_where": "D:\\codes\\Godspeed\\Date\\2025-07-20 尚明伟2", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://fleegix.org"}, "bin": {"ejs": "bin/cli.js"}, "bugs": {"url": "https://github.com/mde/ejs/issues"}, "bundleDependencies": false, "dependencies": {"jake": "^10.8.5"}, "deprecated": false, "description": "Embedded JavaScript templates", "devDependencies": {"browserify": "^16.5.1", "eslint": "^6.8.0", "git-directory-deploy": "^1.5.1", "jsdoc": "^4.0.2", "lru-cache": "^4.0.1", "mocha": "^10.2.0", "uglify-js": "^3.3.16"}, "engines": {"node": ">=0.10.0"}, "homepage": "https://github.com/mde/ejs", "jsdelivr": "ejs.min.js", "keywords": ["template", "engine", "ejs"], "license": "Apache-2.0", "main": "./lib/ejs.js", "name": "ejs", "repository": {"type": "git", "url": "git://github.com/mde/ejs.git"}, "scripts": {"test": "npx jake test"}, "unpkg": "ejs.min.js", "version": "3.1.10"}