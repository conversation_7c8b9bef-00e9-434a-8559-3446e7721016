export declare function degrees2radians(n: number): number;
export declare function max3(a: number, b: number, c: number): number;
export declare function min3(a: number, b: number, c: number): number;
export declare function intInRange(value: number, low: number, high: number): number;
export declare function inRange0to255Rounded(n: number): number;
export declare function inRange0to255(n: number): number;
export declare function stableSort<T>(arrayToSort: T[], callback: (a: T, b: T) => number): T[];
//# sourceMappingURL=arithmetic.d.ts.map