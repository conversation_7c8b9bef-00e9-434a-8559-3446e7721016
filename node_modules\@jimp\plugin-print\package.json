{"_from": "@jimp/plugin-print@^0.22.12", "_id": "@jimp/plugin-print@0.22.12", "_inBundle": false, "_integrity": "sha512-c7TnhHlxm87DJeSnwr/XOLjJU/whoiKYY7r21SbuJ5nuH+7a78EW1teOaj5gEr2wYEd7QtkFqGlmyGXY/YclyQ==", "_location": "/@jimp/plugin-print", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@jimp/plugin-print@^0.22.12", "name": "@jimp/plugin-print", "escapedName": "@jimp%2fplugin-print", "scope": "@jimp", "rawSpec": "^0.22.12", "saveSpec": null, "fetchSpec": "^0.22.12"}, "_requiredBy": ["/@jimp/plugins"], "_resolved": "https://registry.npmmirror.com/@jimp/plugin-print/-/plugin-print-0.22.12.tgz", "_shasum": "6a49020947a9bf21a5a28324425670a25587ca65", "_spec": "@jimp/plugin-print@^0.22.12", "_where": "D:\\codes\\Godspeed\\Date\\2025-07-20 尚明伟2\\node_modules\\@jimp\\plugins", "author": "", "bugs": {"url": "https://github.com/jimp-dev/jimp/issues"}, "bundleDependencies": false, "dependencies": {"@jimp/utils": "^0.22.12", "load-bmfont": "^1.4.1"}, "deprecated": false, "description": "print an image.", "devDependencies": {"@jimp/custom": "^0.22.12", "@jimp/plugin-blit": "^0.22.12", "@jimp/test-utils": "^0.22.12"}, "gitHead": "a4a8d6364bbf97629749e196f3b0a4c94c9a7abc", "homepage": "https://github.com/jimp-dev/jimp#readme", "license": "MIT", "main": "dist/index.js", "module": "es/index.js", "name": "@jimp/plugin-print", "peerDependencies": {"@jimp/custom": ">=0.3.5", "@jimp/plugin-blit": ">=0.3.5"}, "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "git+https://github.com/jimp-dev/jimp.git"}, "scripts": {"build": "npm run build:node:production && npm run build:module", "build:debug": "npm run build:node:debug", "build:module": "cross-env BABEL_ENV=module babel src -d es --source-maps --config-file ../../babel.config.js", "build:node": "babel src -d dist --source-maps --config-file ../../babel.config.js", "build:node:debug": "cross-env BABEL_ENV=development npm run build:node", "build:node:production": "cross-env BABEL_ENV=production npm run build:node", "build:watch": "npm run build:node:debug -- -- --watch --verbose", "test": "cross-env BABEL_ENV=test mocha --require @babel/register --recursive test  --extension js", "test:coverage": "nyc npm run test", "test:watch": "npm run test -- --reporter min --watch"}, "types": "index.d.ts", "version": "0.22.12"}