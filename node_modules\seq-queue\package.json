{"name": "seq-queue", "author": "changchang <<EMAIL>>", "version": "0.0.5", "description": "A simple tool to keep requests to be executed in order.", "homepage": "https://github.com/changchang/seq-queue", "repository": {"type": "git", "url": "**************:changchang/seq-queue.git"}, "dependencies": {}, "devDependencies": {"mocha": ">=0.0.1", "should": ">=0.0.1"}, "_resolved": "https://registry.npmmirror.com/seq-queue/-/seq-queue-0.0.5.tgz", "_integrity": "sha512-hr3Wtp/GZIc/6DAGPDcV4/9WoZhjrkXsi5B/07QgX8tsdc6ilr7BFM6PM6rbdAX1kFSDYeZGLipIZZKyQP0O5Q==", "_from": "seq-queue@^0.0.5"}