{"name": "phin", "version": "3.7.1", "description": "The ultra-lightweight Node.js HTTP client", "main": "lib/phin.js", "types": "types.d.ts", "scripts": {"test": "node ./tests/test.js", "prepublishOnly": "npm test"}, "repository": {"type": "git", "url": "git+https://github.com/ethanent/phin.git"}, "keywords": ["http", "https", "request", "fetch", "ajax", "url", "uri"], "author": "<PERSON>", "license": "MIT", "bugs": {"url": "https://github.com/ethanent/phin/issues"}, "homepage": "https://github.com/ethanent/phin", "files": ["lib/phin.js", "types.d.ts"], "engines": {"node": ">= 8"}, "dependencies": {"centra": "^2.7.0"}, "_resolved": "https://registry.npmmirror.com/phin/-/phin-3.7.1.tgz", "_integrity": "sha512-GEazpTWwTZaEQ9RhL7Nyz0WwqilbqgLahDM3D0hxWwmVDI52nXEybHqiN6/elwpkJBhcuj+WbBu+QfT0uhPGfQ==", "_from": "phin@^3.7.1"}