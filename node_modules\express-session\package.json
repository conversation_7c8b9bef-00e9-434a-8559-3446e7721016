{"_from": "express-session@^1.17.3", "_id": "express-session@1.18.2", "_inBundle": false, "_integrity": "sha512-SZjssGQC7TzTs9rpPDuUrR23GNZ9+2+IkA/+IJWmvQilTr5OSliEHGF+D9scbIpdC6yGtTI0/VhaHoVes2AN/A==", "_location": "/express-session", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "express-session@^1.17.3", "name": "express-session", "escapedName": "express-session", "rawSpec": "^1.17.3", "saveSpec": null, "fetchSpec": "^1.17.3"}, "_requiredBy": ["#USER", "/"], "_resolved": "https://registry.npmmirror.com/express-session/-/express-session-1.18.2.tgz", "_shasum": "34db6252611b57055e877036eea09b4453dec5d8", "_spec": "express-session@^1.17.3", "_where": "D:\\codes\\Godspeed\\Date\\2025-07-20 尚明伟2", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://tjholowaychuk.com"}, "bugs": {"url": "https://github.com/expressjs/session/issues"}, "bundleDependencies": false, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "dependencies": {"cookie": "0.7.2", "cookie-signature": "1.0.7", "debug": "2.6.9", "depd": "~2.0.0", "on-headers": "~1.1.0", "parseurl": "~1.3.3", "safe-buffer": "5.2.1", "uid-safe": "~2.1.5"}, "deprecated": false, "description": "Simple session middleware for Express", "devDependencies": {"after": "0.8.2", "cookie-parser": "1.4.6", "eslint": "8.56.0", "eslint-plugin-markdown": "3.0.1", "express": "4.17.3", "mocha": "10.8.2", "nyc": "15.1.0", "supertest": "6.3.4"}, "engines": {"node": ">= 0.8.0"}, "files": ["session/", "HISTORY.md", "index.js"], "homepage": "https://github.com/expressjs/session#readme", "license": "MIT", "name": "express-session", "repository": {"type": "git", "url": "git+https://github.com/expressjs/session.git"}, "scripts": {"lint": "eslint . && node ./scripts/lint-readme.js", "test": "./test/support/gencert.sh && mocha --require test/support/env --check-leaks --bail --no-exit --reporter spec test/", "test-ci": "nyc --reporter=lcov --reporter=text npm test", "test-cov": "nyc npm test", "version": "node scripts/version-history.js && git add HISTORY.md"}, "version": "1.18.2"}