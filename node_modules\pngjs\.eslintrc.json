{"rules": {"no-alert": "error", "no-array-constructor": "off", "no-bitwise": "off", "no-caller": "error", "no-catch-shadow": "error", "no-class-assign": "error", "no-cond-assign": "error", "no-console": "error", "no-const-assign": "error", "no-constant-condition": "error", "no-continue": "off", "no-control-regex": "error", "no-debugger": "error", "no-delete-var": "error", "no-div-regex": "off", "no-dupe-keys": "error", "no-dupe-args": "error", "no-duplicate-case": "error", "no-else-return": "error", "no-empty": "error", "no-empty-character-class": "error", "no-eq-null": "off", "no-eval": "error", "no-ex-assign": "error", "no-extend-native": "error", "no-extra-bind": "error", "no-extra-boolean-cast": "error", "no-extra-parens": "off", "no-extra-semi": "error", "no-fallthrough": "error", "no-floating-decimal": "error", "no-func-assign": "error", "no-implicit-coercion": "error", "no-implied-eval": "error", "no-inline-comments": "off", "no-inner-declarations": ["error", "functions"], "no-invalid-regexp": "error", "no-invalid-this": "error", "no-irregular-whitespace": "error", "no-iterator": "error", "no-label-var": "error", "no-labels": "error", "no-lone-blocks": "error", "no-lonely-if": "error", "no-loop-func": "error", "no-mixed-requires": ["error", false], "no-mixed-spaces-and-tabs": ["error", false], "linebreak-style": ["off", "unix"], "no-multi-spaces": "error", "no-multi-str": "off", "no-multiple-empty-lines": ["error", {"max": 2}], "no-native-reassign": "error", "no-negated-in-lhs": "error", "no-nested-ternary": "error", "no-new": "error", "no-new-func": "error", "no-new-object": "error", "no-new-require": "error", "no-new-wrappers": "error", "no-obj-calls": "error", "no-octal": "error", "no-octal-escape": "off", "no-param-reassign": "off", "no-path-concat": "off", "no-plusplus": "off", "no-process-env": "off", "no-process-exit": "off", "no-proto": "off", "no-redeclare": "error", "no-regex-spaces": "error", "no-restricted-modules": "off", "no-return-assign": "error", "no-script-url": "off", "no-self-compare": "error", "no-sequences": "error", "no-shadow": "error", "no-shadow-restricted-names": "error", "no-spaced-func": "error", "no-sparse-arrays": "error", "no-sync": "off", "no-ternary": "off", "no-trailing-spaces": "error", "no-this-before-super": "error", "no-throw-literal": "error", "no-undef": "error", "no-undef-init": "error", "no-undefined": "off", "no-unexpected-multiline": "error", "no-underscore-dangle": "off", "no-unneeded-ternary": "off", "no-unreachable": "error", "no-unused-expressions": "error", "no-unused-vars": ["error", {"vars": "all", "args": "after-used"}], "no-use-before-define": "error", "no-useless-call": "error", "no-void": "off", "no-var": "off", "no-warning-comments": ["error", {"terms": ["todo", "fixme", "xxx"], "location": "start"}], "no-with": "error", "array-bracket-spacing": ["error", "never"], "arrow-parens": "error", "arrow-spacing": "error", "accessor-pairs": "error", "block-scoped-var": "off", "brace-style": ["error", "stroust<PERSON>"], "callback-return": ["error", ["callback", "cb", "next"]], "camelcase": ["error", {"properties": "always"}], "comma-dangle": ["error", "never"], "comma-spacing": "error", "comma-style": "error", "complexity": "off", "computed-property-spacing": ["off", "never"], "consistent-return": "off", "consistent-this": ["off", "that"], "constructor-super": "error", "curly": ["error", "all"], "default-case": "error", "dot-location": ["error", "property"], "dot-notation": ["error", {"allowKeywords": true}], "eol-last": "off", "eqeqeq": ["error", "smart"], "func-names": "off", "func-style": ["off", "declaration"], "generator-star-spacing": "off", "guard-for-in": "error", "handle-callback-err": "error", "id-length": ["error", {"min": 3, "max": 25, "exceptions": ["x", "y", "i", "j", "ex", "up"]}], "indent": ["error", 2, {"SwitchCase": 1}], "init-declarations": "off", "key-spacing": ["error", {"beforeColon": false, "afterColon": true}], "keyword-spacing": "error", "lines-around-comment": "off", "max-depth": ["error", 6], "max-len": ["error", {"code": 160}], "max-nested-callbacks": ["error", 1], "max-params": ["error", 6], "max-statements": "off", "new-cap": "error", "new-parens": "error", "newline-after-var": "off", "object-curly-spacing": ["error", "always"], "object-shorthand": "off", "one-var": ["error", {"initialized": "never"}], "operator-assignment": ["off", "always"], "operator-linebreak": ["error", "after"], "padded-blocks": "off", "prefer-const": "off", "prefer-spread": "off", "prefer-reflect": "off", "quote-props": "off", "quotes": ["error", "single"], "radix": "off", "id-match": "off", "require-yield": "off", "semi": ["error", "always"], "semi-spacing": ["error", {"before": false, "after": true}], "sort-vars": "off", "space-before-blocks": ["error", "always"], "space-before-function-paren": ["error", "never"], "space-in-parens": ["error", "never"], "space-infix-ops": "error", "space-unary-ops": ["error", {"words": true, "nonwords": false}], "spaced-comment": "off", "strict": ["error", "global"], "use-isnan": "error", "valid-jsdoc": "off", "valid-typeof": "error", "vars-on-top": "off", "wrap-iife": "off", "wrap-regex": "off", "yoda": ["off", "never"]}, "env": {"node": true, "es6": true}}