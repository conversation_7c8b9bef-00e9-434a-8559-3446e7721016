{"name": "named-placeholders", "version": "1.1.3", "description": "sql named placeholders to unnamed compiler", "main": "index.js", "scripts": {"test": "mocha"}, "repository": {"type": "git", "url": "https://github.com/sidorares/named-placeholders"}, "keywords": ["sql", "pdo", "named", "placeholders"], "engines": {"node": ">=12.0.0"}, "author": "<PERSON><PERSON> <<EMAIL>>", "files": [], "license": "MIT", "devDependencies": {"mocha": "^5.2.0", "should": "^13.2.3"}, "dependencies": {"lru-cache": "^7.14.1"}, "_resolved": "https://registry.npmmirror.com/named-placeholders/-/named-placeholders-1.1.3.tgz", "_integrity": "sha512-eLoBxg6wE/rZkJPhU/xRX1WTpkFEwDJEN96oxFrTsqBdbT5ec295Q+CoHrL9IT0DipqKhmGcaZmwOt8OON5x1w==", "_from": "named-placeholders@^1.1.3"}