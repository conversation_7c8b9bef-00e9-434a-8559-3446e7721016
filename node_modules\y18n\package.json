{"_from": "y18n@^4.0.0", "_id": "y18n@4.0.3", "_inBundle": false, "_integrity": "sha512-JKhqTOwSrqNA1NY5lSztJ1GrBiUodLMmIZuLiDaMRJ+itFd+ABVE8XBjOvIWL+rSqNDC74LCSFmlb/U4UZ4hJQ==", "_location": "/y18n", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "y18n@^4.0.0", "name": "y18n", "escapedName": "y18n", "rawSpec": "^4.0.0", "saveSpec": null, "fetchSpec": "^4.0.0"}, "_requiredBy": ["/yargs"], "_resolved": "https://registry.npmmirror.com/y18n/-/y18n-4.0.3.tgz", "_shasum": "b5f259c82cd6e336921efd7bfd8bf560de9eeedf", "_spec": "y18n@^4.0.0", "_where": "D:\\codes\\Godspeed\\Date\\2025-07-20 尚明伟2\\node_modules\\yargs", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/yargs/y18n/issues"}, "bundleDependencies": false, "deprecated": false, "description": "the bare-bones internationalization library used by yargs", "devDependencies": {"chai": "^4.0.1", "coveralls": "^3.0.0", "mocha": "^4.0.1", "nyc": "^11.0.1", "rimraf": "^2.5.0", "standard": "^10.0.0-beta.0", "standard-version": "^4.2.0"}, "files": ["index.js"], "homepage": "https://github.com/yargs/y18n", "keywords": ["i18n", "internationalization", "yargs"], "license": "ISC", "main": "index.js", "name": "y18n", "repository": {"type": "git", "url": "git+ssh://**************/yargs/y18n.git"}, "scripts": {"coverage": "nyc report --reporter=text-lcov | coveralls", "pretest": "standard", "release": "standard-version", "test": "nyc mocha"}, "version": "4.0.3"}