{"_from": "readable-stream@^4.7.0", "_id": "readable-stream@4.7.0", "_inBundle": false, "_integrity": "sha512-oIGGmcpTLwPga8Bn6/Z75SVaH1z5dUut2ibSyAMVhmUggWpmDn2dapB0n7f8nwaSiRtepAsfJyfXIO5DCVAODg==", "_location": "/readable-stream", "_phantomChildren": {"base64-js": "1.5.1", "ieee754": "1.2.1"}, "_requested": {"type": "range", "registry": true, "raw": "readable-stream@^4.7.0", "name": "readable-stream", "escapedName": "readable-stream", "rawSpec": "^4.7.0", "saveSpec": null, "fetchSpec": "^4.7.0"}, "_requiredBy": ["/readable-web-to-node-stream"], "_resolved": "https://registry.npmmirror.com/readable-stream/-/readable-stream-4.7.0.tgz", "_shasum": "cedbd8a1146c13dfff8dab14068028d58c15ac91", "_spec": "readable-stream@^4.7.0", "_where": "D:\\codes\\Godspeed\\Date\\2025-07-20 尚明伟2\\node_modules\\readable-web-to-node-stream", "browser": {"util": "./lib/ours/util.js", "./lib/ours/index.js": "./lib/ours/browser.js"}, "bugs": {"url": "https://github.com/nodejs/readable-stream/issues"}, "bundleDependencies": false, "dependencies": {"abort-controller": "^3.0.0", "buffer": "^6.0.3", "events": "^3.3.0", "process": "^0.11.10", "string_decoder": "^1.3.0"}, "deprecated": false, "description": "Node.js Streams, a user-land copy of the stream library from Node.js", "devDependencies": {"@babel/core": "^7.17.10", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.16.7", "@babel/plugin-proposal-optional-chaining": "^7.16.7", "@eslint/eslintrc": "^3.2.0", "@rollup/plugin-commonjs": "^22.0.0", "@rollup/plugin-inject": "^4.0.4", "@rollup/plugin-node-resolve": "^13.3.0", "@sinonjs/fake-timers": "^9.1.2", "browserify": "^17.0.0", "c8": "^7.11.2", "esbuild": "^0.19.9", "esbuild-plugin-alias": "^0.2.1", "eslint": "^8.15.0", "eslint-config-standard": "^17.0.0", "eslint-plugin-import": "^2.26.0", "eslint-plugin-n": "^15.2.0", "eslint-plugin-promise": "^6.0.0", "playwright": "^1.21.1", "prettier": "^2.6.2", "rollup": "^2.72.1", "rollup-plugin-polyfill-node": "^0.9.0", "tap": "^16.2.0", "tap-mocha-reporter": "^5.0.3", "tape": "^5.5.3", "tar": "^6.1.11", "undici": "^5.1.1", "webpack": "^5.72.1", "webpack-cli": "^4.9.2"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "files": ["lib", "LICENSE", "README.md"], "homepage": "https://github.com/nodejs/readable-stream", "keywords": ["readable", "stream", "pipe"], "license": "MIT", "licenses": [{"type": "MIT", "url": "https://choosealicense.com/licenses/mit/"}], "main": "lib/ours/index.js", "name": "readable-stream", "repository": {"type": "git", "url": "git://github.com/nodejs/readable-stream.git"}, "scripts": {"build": "node build/build.mjs 18.19.0", "coverage": "c8 -c ./c8.json tap --rcfile=./tap.yml test/parallel/test-*.js test/ours/test-*.js", "format": "prettier -w src lib test", "lint": "eslint src", "postbuild": "prettier -w lib test", "test": "tap --rcfile=./tap.yml test/parallel/test-*.js test/ours/test-*.js", "test:browsers": "node test/browser/runner-browser.mjs", "test:bundlers": "node test/browser/runner-node.mjs", "test:format": "prettier -c src lib test", "test:prepare": "node test/browser/runner-prepare.mjs", "test:readable-stream-only": "node readable-stream-test/runner-prepare.mjs"}, "version": "4.7.0"}