{"_from": "xml-parse-from-string@^1.0.0", "_id": "xml-parse-from-string@1.0.1", "_inBundle": false, "_integrity": "sha512-ErcKwJTF54uRzzNMXq2X5sMIy88zJvfN2DmdoQvy7PAFJ+tPRU6ydWuOKNMyfmOjdyBQTFREi60s0Y0SyI0G0g==", "_location": "/xml-parse-from-string", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "xml-parse-from-string@^1.0.0", "name": "xml-parse-from-string", "escapedName": "xml-parse-from-string", "rawSpec": "^1.0.0", "saveSpec": null, "fetchSpec": "^1.0.0"}, "_requiredBy": ["/parse-bmfont-xml"], "_resolved": "https://registry.npmmirror.com/xml-parse-from-string/-/xml-parse-from-string-1.0.1.tgz", "_shasum": "a9029e929d3dbcded169f3c6e28238d95a5d5a28", "_spec": "xml-parse-from-string@^1.0.0", "_where": "D:\\codes\\Godspeed\\Date\\2025-07-20 尚明伟2\\node_modules\\parse-bmfont-xml", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/mattdesl"}, "bugs": {"url": "https://github.com/Jam3/xml-parse-from-string/issues"}, "bundleDependencies": false, "dependencies": {}, "deprecated": false, "description": "DOMParser.parseFromString for XML with IE8 fallback", "devDependencies": {"brfs": "^1.4.0", "browserify": "^9.0.3", "faucet": "0.0.1", "tape": "^3.5.0", "testling": "^1.7.1"}, "homepage": "https://github.com/Jam3/xml-parse-from-string", "keywords": ["ie8", "fallback", "dom", "parser", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "xml", "string", "parse", "browser", "browserify", "webpack", "activeXObject"], "license": "MIT", "main": "index.js", "name": "xml-parse-from-string", "repository": {"type": "git", "url": "git://github.com/Jam3/xml-parse-from-string.git"}, "scripts": {"test": "browserify test.js -t brfs | testling | faucet"}, "version": "1.0.1"}