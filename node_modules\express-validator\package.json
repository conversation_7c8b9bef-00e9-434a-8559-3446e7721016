{"_from": "express-validator@6.15.0", "_id": "express-validator@6.15.0", "_inBundle": false, "_integrity": "sha512-r05VYoBL3i2pswuehoFSy+uM8NBuVaY7avp5qrYjQBDzagx2Z5A77FZqPT8/gNLF3HopWkIzaTFaC4JysWXLqg==", "_location": "/express-validator", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "express-validator@6.15.0", "name": "express-validator", "escapedName": "express-validator", "rawSpec": "6.15.0", "saveSpec": null, "fetchSpec": "6.15.0"}, "_requiredBy": ["#USER", "/"], "_resolved": "https://registry.npmmirror.com/express-validator/-/express-validator-6.15.0.tgz", "_shasum": "5e4601428960b0d66f5f4ae09cb32ed2077374a4", "_spec": "express-validator@6.15.0", "_where": "D:\\codes\\Godspeed\\Date\\2025-07-20 尚明伟2", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/express-validator/express-validator/issues"}, "bundleDependencies": false, "contributors": [{"name": "<PERSON>", "email": "<PERSON><PERSON><PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "dependencies": {"lodash": "^4.17.21", "validator": "^13.9.0"}, "deprecated": false, "description": "Express middleware for the validator module.", "devDependencies": {"@docusaurus/core": "^2.2.0", "@docusaurus/plugin-client-redirects": "^2.2.0", "@docusaurus/preset-classic": "^2.2.0", "@types/jest": "^26.0.20", "@types/lodash": "^4.14.168", "@typescript-eslint/eslint-plugin": "^5.47.1", "@typescript-eslint/parser": "^5.47.1", "eslint": "^8.30.0", "eslint-config-prettier": "^8.5.0", "eslint-plugin-import": "^2.26.0", "eslint-plugin-prettier": "^4.2.1", "jest": "^26.6.3", "prettier": "^2.8.1", "ts-jest": "^26.4.4", "typescript": "~4.3.5"}, "engines": {"node": ">= 8.0.0"}, "files": ["src", "check", "filter", "!**/*.spec.ts", "!**/*.ts", "**/*.d.ts", "!website", "!docs"], "homepage": "https://express-validator.github.io", "keywords": ["express", "validator", "validation", "validate", "sanitize", "sanitization", "xss"], "license": "MIT", "main": "./src/index.js", "name": "express-validator", "repository": {"type": "git", "url": "git://github.com/express-validator/express-validator.git"}, "scripts": {"build": "tsc", "clean": "git clean -Xf src check filter", "docs:build": "npm --prefix ./website run build", "docs:publish": "USE_SSH=true DEPLOYMENT_BRANCH=master npm --prefix ./website run publish-gh-pages", "docs:serve": "npm --prefix ./website run serve", "docs:start": "npm --prefix ./website start", "docs:version": "npm --prefix ./website run version", "lint": "eslint --ignore-path .gitignore 'src/**/*.ts' && prettier -c .", "postpublish": "npm run docs:publish", "prepublishOnly": "tsc", "test": "jest"}, "types": "./src/index.d.ts", "version": "6.15.0"}