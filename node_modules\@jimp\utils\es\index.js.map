{"version": 3, "file": "index.js", "names": ["isNodePattern", "cb", "TypeError", "throwError", "error", "Error", "call", "scan", "image", "x", "y", "w", "h", "f", "Math", "round", "_y", "_x", "idx", "bitmap", "width", "scanIterator"], "sources": ["../src/index.js"], "sourcesContent": ["export function isNodePattern(cb) {\n  if (typeof cb === \"undefined\") {\n    return false;\n  }\n\n  if (typeof cb !== \"function\") {\n    throw new TypeError(\"Callback must be a function\");\n  }\n\n  return true;\n}\n\nexport function throwError(error, cb) {\n  if (typeof error === \"string\") {\n    error = new Error(error);\n  }\n\n  if (typeof cb === \"function\") {\n    return cb.call(this, error);\n  }\n\n  throw error;\n}\n\nexport function scan(image, x, y, w, h, f) {\n  // round input\n  x = Math.round(x);\n  y = Math.round(y);\n  w = Math.round(w);\n  h = Math.round(h);\n\n  for (let _y = y; _y < y + h; _y++) {\n    for (let _x = x; _x < x + w; _x++) {\n      const idx = (image.bitmap.width * _y + _x) << 2;\n      f.call(image, _x, _y, idx);\n    }\n  }\n\n  return image;\n}\n\nexport function* scanIterator(image, x, y, w, h) {\n  // round input\n  x = Math.round(x);\n  y = Math.round(y);\n  w = Math.round(w);\n  h = Math.round(h);\n\n  for (let _y = y; _y < y + h; _y++) {\n    for (let _x = x; _x < x + w; _x++) {\n      const idx = (image.bitmap.width * _y + _x) << 2;\n      yield { x: _x, y: _y, idx, image };\n    }\n  }\n}\n"], "mappings": "AAAA,OAAO,SAASA,aAAa,CAACC,EAAE,EAAE;EAChC,IAAI,OAAOA,EAAE,KAAK,WAAW,EAAE;IAC7B,OAAO,KAAK;EACd;EAEA,IAAI,OAAOA,EAAE,KAAK,UAAU,EAAE;IAC5B,MAAM,IAAIC,SAAS,CAAC,6BAA6B,CAAC;EACpD;EAEA,OAAO,IAAI;AACb;AAEA,OAAO,SAASC,UAAU,CAACC,KAAK,EAAEH,EAAE,EAAE;EACpC,IAAI,OAAOG,KAAK,KAAK,QAAQ,EAAE;IAC7BA,KAAK,GAAG,IAAIC,KAAK,CAACD,KAAK,CAAC;EAC1B;EAEA,IAAI,OAAOH,EAAE,KAAK,UAAU,EAAE;IAC5B,OAAOA,EAAE,CAACK,IAAI,CAAC,IAAI,EAAEF,KAAK,CAAC;EAC7B;EAEA,MAAMA,KAAK;AACb;AAEA,OAAO,SAASG,IAAI,CAACC,KAAK,EAAEC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAE;EACzC;EACAJ,CAAC,GAAGK,IAAI,CAACC,KAAK,CAACN,CAAC,CAAC;EACjBC,CAAC,GAAGI,IAAI,CAACC,KAAK,CAACL,CAAC,CAAC;EACjBC,CAAC,GAAGG,IAAI,CAACC,KAAK,CAACJ,CAAC,CAAC;EACjBC,CAAC,GAAGE,IAAI,CAACC,KAAK,CAACH,CAAC,CAAC;EAEjB,KAAK,IAAII,EAAE,GAAGN,CAAC,EAAEM,EAAE,GAAGN,CAAC,GAAGE,CAAC,EAAEI,EAAE,EAAE,EAAE;IACjC,KAAK,IAAIC,EAAE,GAAGR,CAAC,EAAEQ,EAAE,GAAGR,CAAC,GAAGE,CAAC,EAAEM,EAAE,EAAE,EAAE;MACjC,MAAMC,GAAG,GAAIV,KAAK,CAACW,MAAM,CAACC,KAAK,GAAGJ,EAAE,GAAGC,EAAE,IAAK,CAAC;MAC/CJ,CAAC,CAACP,IAAI,CAACE,KAAK,EAAES,EAAE,EAAED,EAAE,EAAEE,GAAG,CAAC;IAC5B;EACF;EAEA,OAAOV,KAAK;AACd;AAEA,OAAO,UAAUa,YAAY,CAACb,KAAK,EAAEC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAE;EAC/C;EACAH,CAAC,GAAGK,IAAI,CAACC,KAAK,CAACN,CAAC,CAAC;EACjBC,CAAC,GAAGI,IAAI,CAACC,KAAK,CAACL,CAAC,CAAC;EACjBC,CAAC,GAAGG,IAAI,CAACC,KAAK,CAACJ,CAAC,CAAC;EACjBC,CAAC,GAAGE,IAAI,CAACC,KAAK,CAACH,CAAC,CAAC;EAEjB,KAAK,IAAII,EAAE,GAAGN,CAAC,EAAEM,EAAE,GAAGN,CAAC,GAAGE,CAAC,EAAEI,EAAE,EAAE,EAAE;IACjC,KAAK,IAAIC,EAAE,GAAGR,CAAC,EAAEQ,EAAE,GAAGR,CAAC,GAAGE,CAAC,EAAEM,EAAE,EAAE,EAAE;MACjC,MAAMC,GAAG,GAAIV,KAAK,CAACW,MAAM,CAACC,KAAK,GAAGJ,EAAE,GAAGC,EAAE,IAAK,CAAC;MAC/C,MAAM;QAAER,CAAC,EAAEQ,EAAE;QAAEP,CAAC,EAAEM,EAAE;QAAEE,GAAG;QAAEV;MAAM,CAAC;IACpC;EACF;AACF"}