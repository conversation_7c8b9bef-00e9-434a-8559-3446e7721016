{"_from": "@jimp/plugins@^0.22.12", "_id": "@jimp/plugins@0.22.12", "_inBundle": false, "_integrity": "sha512-yBJ8vQrDkBbTgQZLty9k4+KtUQdRjsIDJSPjuI21YdVeqZxYywifHl4/XWILoTZsjTUASQcGoH0TuC0N7xm3ww==", "_location": "/@jimp/plugins", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@jimp/plugins@^0.22.12", "name": "@jimp/plugins", "escapedName": "@jimp%2fplugins", "scope": "@jimp", "rawSpec": "^0.22.12", "saveSpec": null, "fetchSpec": "^0.22.12"}, "_requiredBy": ["/jimp"], "_resolved": "https://registry.npmmirror.com/@jimp/plugins/-/plugins-0.22.12.tgz", "_shasum": "45a3b96d2d24cec21d4f8b79d1cfcec6fcb2f1d4", "_spec": "@jimp/plugins@^0.22.12", "_where": "D:\\codes\\Godspeed\\Date\\2025-07-20 尚明伟2\\node_modules\\jimp", "author": "", "bugs": {"url": "https://github.com/jimp-dev/jimp/issues"}, "bundleDependencies": false, "dependencies": {"@jimp/plugin-blit": "^0.22.12", "@jimp/plugin-blur": "^0.22.12", "@jimp/plugin-circle": "^0.22.12", "@jimp/plugin-color": "^0.22.12", "@jimp/plugin-contain": "^0.22.12", "@jimp/plugin-cover": "^0.22.12", "@jimp/plugin-crop": "^0.22.12", "@jimp/plugin-displace": "^0.22.12", "@jimp/plugin-dither": "^0.22.12", "@jimp/plugin-fisheye": "^0.22.12", "@jimp/plugin-flip": "^0.22.12", "@jimp/plugin-gaussian": "^0.22.12", "@jimp/plugin-invert": "^0.22.12", "@jimp/plugin-mask": "^0.22.12", "@jimp/plugin-normalize": "^0.22.12", "@jimp/plugin-print": "^0.22.12", "@jimp/plugin-resize": "^0.22.12", "@jimp/plugin-rotate": "^0.22.12", "@jimp/plugin-scale": "^0.22.12", "@jimp/plugin-shadow": "^0.22.12", "@jimp/plugin-threshold": "^0.22.12", "timm": "^1.6.1"}, "deprecated": false, "description": "Default <PERSON> plugin.", "gitHead": "a4a8d6364bbf97629749e196f3b0a4c94c9a7abc", "homepage": "https://github.com/jimp-dev/jimp#readme", "license": "MIT", "main": "dist/index.js", "module": "es/index.js", "name": "@jimp/plugins", "peerDependencies": {"@jimp/custom": ">=0.3.5"}, "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "git+https://github.com/jimp-dev/jimp.git"}, "scripts": {"build": "npm run build:node:production && npm run build:module", "build:debug": "npm run build:node:debug", "build:module": "cross-env BABEL_ENV=module babel src -d es --source-maps --config-file ../../babel.config.js", "build:node": "babel src -d dist --source-maps --config-file ../../babel.config.js", "build:node:debug": "cross-env BABEL_ENV=development npm run build:node", "build:node:production": "cross-env BABEL_ENV=production npm run build:node", "build:watch": "npm run build:node:debug -- -- --watch --verbose"}, "types": "index.d.ts", "version": "0.22.12"}