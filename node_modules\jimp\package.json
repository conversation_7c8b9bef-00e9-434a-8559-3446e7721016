{"_from": "jimp@0.22.10", "_id": "jimp@0.22.10", "_inBundle": false, "_integrity": "sha512-lCaHIJAgTOsplyJzC1w/laxSxrbSsEBw4byKwXgUdMmh+ayPsnidTblenQm+IvhIs44Gcuvlb6pd2LQ0wcKaKg==", "_location": "/jimp", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "jimp@0.22.10", "name": "jimp", "escapedName": "jimp", "rawSpec": "0.22.10", "saveSpec": null, "fetchSpec": "0.22.10"}, "_requiredBy": ["#USER", "/"], "_resolved": "https://registry.npmmirror.com/jimp/-/jimp-0.22.10.tgz", "_shasum": "2cc3e265a99cdbe69ec60ddd57cbcde6a6cf0519", "_spec": "jimp@0.22.10", "_where": "D:\\codes\\Godspeed\\Date\\2025-07-20 尚明伟2", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "browser": "browser/lib/jimp.js", "bugs": {"url": "https://github.com/jimp-dev/jimp/issues"}, "bundleDependencies": false, "dependencies": {"@jimp/custom": "^0.22.10", "@jimp/plugins": "^0.22.10", "@jimp/types": "^0.22.10", "regenerator-runtime": "^0.13.3"}, "deprecated": false, "description": "An image processing library written entirely in JavaScript (i.e. zero external or native dependencies)", "devDependencies": {"@jimp/test-utils": "^0.22.10", "empty-module": "^0.0.2", "express": "^4.17.1", "path-browserify": "^1.0.1", "webpack": "^5.75.0", "webpack-cli": "^5.0.1"}, "files": ["browser", "dist", "es", "index.d.ts", "fonts", "types"], "gitHead": "af334eff862cc8091378cd6b1e194086e36d445e", "homepage": "https://github.com/jimp-dev/jimp#readme", "keywords": ["image", "image processing", "image manipulation", "png", "jpg", "jpeg", "bmp", "resize", "scale", "crop"], "license": "MIT", "main": "dist/index.js", "module": "es/index.js", "name": "jimp", "nyc": {"sourceMap": false, "instrument": false, "reporter": ["text", "text-summary", "lcov", "html"], "exclude": ["src/modules/*.js", "test/*.js"]}, "repository": {"type": "git", "url": "git+https://github.com/jimp-dev/jimp.git"}, "scripts": {"build": "npm run build:browser && npm run build:node:production && npm run build:module", "build:browser": "cross-env NODE_ENV=production webpack", "build:browser:debug": "cross-env NODE_ENV=development ENV=browser webpack", "build:debug": "npm run build:browser:debug && npm run build:node:debug", "build:module": "cross-env BABEL_ENV=module babel src -d es --source-maps --config-file ../../babel.config.js", "build:node": "babel src -d dist --source-maps --config-file ../../babel.config.js", "build:node:debug": "cross-env BABEL_ENV=development npm run build:node", "build:node:production": "cross-env BABEL_ENV=production npm run build:node", "build:watch": "npm run build:node:debug -- -- --watch --verbose", "test": "cross-env BABEL_ENV=test mocha --require @babel/register --recursive test --extension js", "test:coverage": "nyc npm run test", "test:watch": "npm run test -- --reporter min --watch"}, "tonicExampleFilename": "example.js", "types": "types/index.d.ts", "version": "0.22.10"}