{"_from": "xhr@^2.0.1", "_id": "xhr@2.6.0", "_inBundle": false, "_integrity": "sha512-/eCGLb5rxjx5e3mF1A7s+pLlR6CGyqWN91fv1JgER5mVWg1MZmlhBvy9kjcsOdRk8RrIujotWyJamfyrp+WIcA==", "_location": "/xhr", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "xhr@^2.0.1", "name": "xhr", "escapedName": "xhr", "rawSpec": "^2.0.1", "saveSpec": null, "fetchSpec": "^2.0.1"}, "_requiredBy": ["/load-bmfont"], "_resolved": "https://registry.npmmirror.com/xhr/-/xhr-2.6.0.tgz", "_shasum": "b69d4395e792b4173d6b7df077f0fc5e4e2b249d", "_spec": "xhr@^2.0.1", "_where": "D:\\codes\\Godspeed\\Date\\2025-07-20 尚明伟2\\node_modules\\load-bmfont", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/naugtur/xhr/issues", "email": "<EMAIL>"}, "bundleDependencies": false, "contributors": [{"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dependencies": {"global": "~4.4.0", "is-function": "^1.0.1", "parse-headers": "^2.0.0", "xtend": "^4.0.0"}, "deprecated": false, "description": "small xhr abstraction", "devDependencies": {"for-each": "^0.3.2", "pre-commit": "1.2.2", "run-browser": "github:naugtur/run-browser", "tap-spec": "^4.0.2", "tape": "^4.0.0"}, "homepage": "https://github.com/naugtur/xhr", "keywords": ["xhr", "http", "xmlhttprequest", "xhr2", "browserify"], "license": "MIT", "main": "index", "name": "xhr", "repository": {"type": "git", "url": "git://github.com/naugtur/xhr.git"}, "scripts": {"browser": "run-browser -m test/mock-server.js test/index.js", "test": "run-browser test/index.js -b -m test/mock-server.js | tap-spec"}, "typings": "./index.d.ts", "version": "2.6.0"}